import { Subscription } from "rxjs";
import { finalize, tap } from "rxjs/operators";
import { RealTimeDataStream } from "../data/realtime-stream.ts";
import { SingleBarStrategy } from "../strategy/single-bar.ts";
import { GapHandler } from "../trading/gap-handler.ts";
import { OrderManager } from "../trading/order-manager.ts";
import { PositionManager } from "../trading/position-manager.ts";
import type { AppConfig } from "../types/config.ts";
import type { MarketTick } from "../types/tick.ts";
import type { TradeSignal } from "../types/trade.ts";

/**
 * 回测引擎
 * 协调各模块运行回测，模拟真实交易环境
 */
export class BacktestEngine {
  private config: AppConfig;
  private dataStream: RealTimeDataStream;
  private strategy: SingleBarStrategy;
  private positionManager: PositionManager;
  private orderManager: OrderManager;
  private gapHandler: GapHandler;

  private subscription?: Subscription;
  private isRunning = false;
  private startTime?: Date;
  private endTime?: Date;

  // 统计信息
  private ticksProcessed = 0;
  private candlesCompleted = 0;
  private signalsGenerated = 0;
  private tradesExecuted = 0;

  constructor(config: AppConfig) {
    this.config = config;

    // 初始化各模块
    this.dataStream = new RealTimeDataStream(config.stream);
    this.strategy = new SingleBarStrategy(config.strategy);
    this.positionManager = new PositionManager(config.strategy);
    this.orderManager = new OrderManager(config.backtest);
    this.gapHandler = new GapHandler(config.backtest.minTick, config.backtest.minTick);

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 策略信号处理
    this.strategy.onSignal((signal: TradeSignal) => {
      this.handleSignal(signal);
    });

    // K线完成处理
    this.strategy.onCandleComplete((candle) => {
      this.candlesCompleted++;
      if (this.config.output.showProgress && this.candlesCompleted % 100 === 0) {
        console.log(`已完成 ${this.candlesCompleted} 根K线`);
      }
    });

    // 订单成交处理
    this.orderManager.onOrderFilled((order, fillPrice) => {
      this.handleOrderFilled(order, fillPrice);
    });
  }

  /**
   * 运行回测
   */
  async run(): Promise<void> {
    if (this.isRunning) {
      console.warn("回测已在运行中");
      return;
    }

    try {
      console.log("开始初始化回测引擎...");

      // 初始化数据流
      await this.dataStream.initialize(this.config.backtest.dataFile);

      // 初始化策略
      this.strategy.initialize();

      console.log("回测引擎初始化完成，开始回测...");
      this.startTime = new Date();
      this.isRunning = true;

      // 创建数据流并订阅
      const stream$ = this.dataStream.createStream();

      this.subscription = stream$
        .pipe(
          tap((tick: MarketTick) => this.processTick(tick)),
          finalize(() => this.onBacktestComplete())
        )
        .subscribe({
          error: (error) => {
            console.error("回测过程中发生错误:", error);
            this.stop();
          },
        });

      // 等待回测完成
      await new Promise<void>((resolve) => {
        const checkComplete = () => {
          if (!this.isRunning) {
            resolve();
          } else {
            setTimeout(checkComplete, 100);
          }
        };
        checkComplete();
      });
    } catch (error) {
      console.error("回测初始化失败:", error);
      throw error;
    }
  }

  /**
   * 处理单个tick数据
   * @param tick 市场数据
   */
  private processTick(tick: MarketTick): void {
    this.ticksProcessed++;

    // 1. 检测跳空
    const gapInfo =
      tick.isSessionStart && this.strategy.getCompletedCandles().length > 0
        ? this.gapHandler.detectGap(tick, this.getLastTick())
        : undefined;

    // 2. 处理订单执行
    const filledOrders = this.orderManager.processTick(tick, gapInfo);

    // 3. 更新策略状态
    this.strategy.onTick(tick);

    // 4. 更新持仓状态
    this.positionManager.updatePosition(tick.close);

    // 5. 检查止损止盈
    this.checkStopLossAndTakeProfit(tick);

    // 显示进度
    if (this.config.output.showProgress && this.ticksProcessed % 10000 === 0) {
      const progress = this.dataStream.getProgress();
      console.log(`回测进度: ${progress.toFixed(1)}% (${this.ticksProcessed} ticks)`);
    }
  }

  /**
   * 处理交易信号
   * @param signal 交易信号
   */
  private handleSignal(signal: TradeSignal): void {
    this.signalsGenerated++;

    console.log(`收到交易信号: ${signal.type} @ ${signal.price}`);

    if (signal.type === "CLOSE_ALL") {
      // 强制平仓
      if (this.positionManager.hasPosition()) {
        const trade = this.positionManager.closePosition(signal.price, signal.time, signal.reason);
        if (trade) {
          this.tradesExecuted++;
          console.log(`强制平仓完成: 盈亏 ${trade.netPnl.toFixed(2)}`);
        }
      }
      this.orderManager.cancelAllOrders();
    } else {
      // 入场信号
      const order = this.orderManager.createOrderFromSignal(signal);

      // 创建止损止盈订单
      if (signal.stopLoss) {
        this.orderManager.createStopLossOrder(
          order.id,
          signal.type === "ENTRY_LONG" ? "LONG" : "SHORT",
          signal.quantity,
          signal.stopLoss,
          signal.time
        );
      }

      if (signal.takeProfit) {
        this.orderManager.createTakeProfitOrder(
          order.id,
          signal.type === "ENTRY_LONG" ? "LONG" : "SHORT",
          signal.quantity,
          signal.takeProfit,
          signal.time
        );
      }
    }
  }

  /**
   * 处理订单成交
   * @param order 成交订单
   * @param fillPrice 成交价格
   */
  private handleOrderFilled(order: any, fillPrice: number): void {
    if (order.tag === "ENTRY_LONG" || order.tag === "ENTRY_SHORT") {
      // 开仓
      const success = this.positionManager.openPosition(
        order.side,
        order.quantity,
        fillPrice,
        order.fillTime!,
        undefined, // 止损价格由止损订单管理
        undefined, // 止盈价格由止盈订单管理
        false // TODO: 检测是否因跳空调整
      );

      if (success) {
        console.log(`开仓成功: ${order.side} ${order.quantity}手 @ ${fillPrice}`);
      }
    } else if (order.tag === "STOP_LOSS" || order.tag === "TAKE_PROFIT") {
      // 平仓
      const trade = this.positionManager.closePosition(
        fillPrice,
        order.fillTime!,
        order.tag === "STOP_LOSS" ? "止损" : "止盈"
      );

      if (trade) {
        this.tradesExecuted++;
        console.log(
          `平仓完成: ${trade.side} ${trade.quantity}手 @ ${fillPrice}, 盈亏: ${trade.netPnl.toFixed(2)}`
        );

        // 取消其他相关订单
        this.orderManager.cancelChildOrders(order.parentOrderId!);
      }
    }
  }

  /**
   * 检查止损止盈
   * @param tick 当前tick
   */
  private checkStopLossAndTakeProfit(tick: MarketTick): void {
    if (!this.positionManager.hasPosition()) return;

    const position = this.positionManager.getCurrentPosition()!;

    // 检查止损
    if (this.positionManager.checkStopLoss(tick.close)) {
      const trade = this.positionManager.closePosition(tick.close, tick.datetime, "止损触发");
      if (trade) {
        this.tradesExecuted++;
        console.log(`止损触发: 盈亏 ${trade.netPnl.toFixed(2)}`);
      }
      this.orderManager.cancelAllOrders();
    }
    // 检查止盈
    else if (this.positionManager.checkTakeProfit(tick.close)) {
      const trade = this.positionManager.closePosition(tick.close, tick.datetime, "止盈触发");
      if (trade) {
        this.tradesExecuted++;
        console.log(`止盈触发: 盈亏 ${trade.netPnl.toFixed(2)}`);
      }
      this.orderManager.cancelAllOrders();
    }
  }

  /**
   * 获取最后一个tick（用于跳空检测）
   */
  private getLastTick(): MarketTick {
    // 这里需要实现获取前一个tick的逻辑
    // 简化处理，返回一个默认值
    return {
      datetime: "",
      open: 0,
      high: 0,
      low: 0,
      close: 0,
      volume: 0,
    };
  }

  /**
   * 回测完成处理
   */
  private onBacktestComplete(): void {
    this.endTime = new Date();
    this.isRunning = false;

    console.log("\n=== 回测完成 ===");
    console.log(`回测时长: ${this.endTime.getTime() - this.startTime!.getTime()}ms`);
    console.log(`处理tick数: ${this.ticksProcessed}`);
    console.log(`完成K线数: ${this.candlesCompleted}`);
    console.log(`生成信号数: ${this.signalsGenerated}`);
    console.log(`执行交易数: ${this.tradesExecuted}`);

    // 输出交易统计
    const stats = this.positionManager.getTradeStats();
    console.log("\n=== 交易统计 ===");
    console.log(`总交易次数: ${stats.totalTrades}`);
    console.log(`胜率: ${stats.winRate.toFixed(2)}%`);
    console.log(`总盈亏: ${stats.totalPnl.toFixed(2)}`);
    console.log(`最大盈利: ${stats.maxProfit.toFixed(2)}`);
    console.log(`最大亏损: ${stats.maxLoss.toFixed(2)}`);
  }

  /**
   * 停止回测
   */
  stop(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.isRunning = false;
    console.log("回测已停止");
  }

  /**
   * 获取回测结果
   */
  getResults() {
    return {
      trades: this.positionManager.getTradeHistory(),
      stats: this.positionManager.getTradeStats(),
      candles: this.strategy.getCompletedCandles(),
      orders: this.orderManager.getAllOrders(),
    };
  }

  /**
   * 获取回测状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      ticksProcessed: this.ticksProcessed,
      candlesCompleted: this.candlesCompleted,
      signalsGenerated: this.signalsGenerated,
      tradesExecuted: this.tradesExecuted,
      progress: this.dataStream.getProgress(),
    };
  }
}
