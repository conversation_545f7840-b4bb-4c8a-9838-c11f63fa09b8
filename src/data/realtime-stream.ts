import { Observable, timer, EMPTY } from "rxjs";
import { map, takeWhile, tap, share } from "rxjs/operators";
import { MarketTick, StreamState } from "../types/tick.ts";
import { StreamConfig } from "../types/config.ts";
import { DataLoader } from "./data-loader.ts";

/**
 * 实时数据流
 * 模拟WebSocket实时数据推送，每5秒推送一次市场数据
 */
export class RealTimeDataStream {
  private data: MarketTick[] = [];
  private currentIndex = 0;
  private streamState: StreamState;
  private config: StreamConfig;
  private dataLoader: DataLoader;

  constructor(config: StreamConfig) {
    this.config = config;
    this.dataLoader = new DataLoader();
    this.streamState = {
      currentIndex: 0,
      totalCount: 0,
      isCompleted: false,
      intervalMs: config.intervalMs,
    };
  }

  /**
   * 初始化数据流
   * @param dataFile 数据文件路径
   */
  async initialize(dataFile: string): Promise<void> {
    console.log(`正在加载数据文件: ${dataFile}`);
    this.data = await this.dataLoader.loadData(dataFile);
    this.streamState.totalCount = this.data.length;
    this.currentIndex = 0;
    
    const stats = this.dataLoader.getStats();
    console.log(`数据流初始化完成:`, stats);
  }

  /**
   * 创建实时数据流
   * 回测模式：直接同步处理所有数据
   * 实时模式：模拟WebSocket每5秒推送一次数据
   * @returns Observable<MarketTick>
   */
  createStream(): Observable<MarketTick> {
    if (this.data.length === 0) {
      console.error("数据流未初始化，请先调用 initialize() 方法");
      return EMPTY;
    }

    console.log(`开始数据流推送，模式: ${this.config.fastMode ? '快速回测' : '实时模拟'}`);
    console.log(`总数据量: ${this.data.length}`);

    if (this.config.fastMode && this.config.intervalMs === 0) {
      // 回测模式：直接同步处理所有数据
      return new Observable<MarketTick>(subscriber => {
        console.log("回测模式：开始同步处理数据...");

        for (let i = 0; i < this.data.length; i++) {
          const tick = this.data[i];
          if (tick) {
            this.currentIndex = i + 1;
            this.updateStreamState();

            // 显示进度
            if (i % 50000 === 0) {
              this.logProgress();
            }

            subscriber.next(tick);
          }
        }

        console.log("数据流推送完成");
        subscriber.complete();
      }).pipe(share());
    } else {
      // 实时模式：使用定时器
      const interval = this.config.intervalMs || 5000;
      console.log(`推送间隔: ${interval}ms`);

      return timer(0, interval).pipe(
        map(() => this.getNextTick()),
        takeWhile(tick => tick !== null),
        tap(tick => {
          if (tick) {
            this.updateStreamState();
            if (this.currentIndex % 1000 === 0) {
              this.logProgress();
            }
          }
        }),
        share()
      ) as Observable<MarketTick>;
    }
  }

  /**
   * 获取下一个tick数据
   * @returns MarketTick | null
   */
  private getNextTick(): MarketTick | null {
    if (this.currentIndex >= this.data.length) {
      this.streamState.isCompleted = true;
      console.log("数据流推送完成");
      return null;
    }

    const tick = this.data[this.currentIndex];
    this.currentIndex++;
    
    return tick || null;
  }

  /**
   * 更新流状态
   */
  private updateStreamState(): void {
    this.streamState.currentIndex = this.currentIndex;
    this.streamState.isCompleted = this.currentIndex >= this.data.length;
  }

  /**
   * 记录进度日志
   */
  private logProgress(): void {
    const progress = (this.currentIndex / this.data.length * 100).toFixed(1);
    const currentTick = this.data[this.currentIndex - 1];
    console.log(`数据流进度: ${progress}% (${this.currentIndex}/${this.data.length}) - ${currentTick?.datetime}`);
  }

  /**
   * 获取流状态
   * @returns StreamState
   */
  getStreamState(): StreamState {
    return { ...this.streamState };
  }

  /**
   * 获取进度百分比
   * @returns number (0-100)
   */
  getProgress(): number {
    if (this.streamState.totalCount === 0) return 0;
    return (this.streamState.currentIndex / this.streamState.totalCount) * 100;
  }

  /**
   * 是否已完成
   * @returns boolean
   */
  isCompleted(): boolean {
    return this.streamState.isCompleted;
  }

  /**
   * 重置数据流
   */
  reset(): void {
    this.currentIndex = 0;
    this.streamState = {
      currentIndex: 0,
      totalCount: this.data.length,
      isCompleted: false,
      intervalMs: this.config.intervalMs,
    };
    console.log("数据流已重置");
  }

  /**
   * 跳转到指定位置
   * @param index 目标索引
   */
  seekTo(index: number): void {
    if (index >= 0 && index < this.data.length) {
      this.currentIndex = index;
      this.updateStreamState();
      console.log(`数据流跳转到位置: ${index}`);
    } else {
      console.warn(`无效的跳转位置: ${index}`);
    }
  }

  /**
   * 获取当前tick数据（不推进索引）
   * @returns MarketTick | null
   */
  getCurrentTick(): MarketTick | null {
    if (this.currentIndex >= this.data.length) return null;
    return this.data[this.currentIndex] || null;
  }

  /**
   * 预览接下来的N个tick
   * @param count 预览数量
   * @returns MarketTick[]
   */
  previewNext(count: number): MarketTick[] {
    const endIndex = Math.min(this.currentIndex + count, this.data.length);
    return this.data.slice(this.currentIndex, endIndex);
  }

  /**
   * 获取数据统计信息
   */
  getDataStats() {
    return this.dataLoader.getStats();
  }
}
