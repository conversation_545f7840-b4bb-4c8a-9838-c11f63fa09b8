import csvParser from "csv-parser";
import { createReadStream } from "fs-extra";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { MarketTick, RawCsvRow, GapDetection } from "../types/tick.ts";

// 扩展dayjs功能
dayjs.extend(utc);

/**
 * CSV数据加载器
 * 负责从CSV文件加载历史数据并转换为MarketTick格式
 */
export class DataLoader {
  private data: MarketTick[] = [];

  /**
   * 从CSV文件加载数据
   * @param filePath CSV文件路径
   * @returns Promise<MarketTick[]>
   */
  async loadData(filePath: string): Promise<MarketTick[]> {
    return new Promise((resolve, reject) => {
      this.data = [];
      
      createReadStream(filePath)
        .pipe(csvParser())
        .on("data", (row: RawCsvRow) => {
          try {
            const tick = this.parseRow(row);
            if (tick && this.isValidTradingTime(tick)) {
              this.data.push(tick);
            }
          } catch (error) {
            console.warn(`解析数据行失败: ${error}`);
          }
        })
        .on("end", () => {
          console.log(`成功加载 ${this.data.length} 条数据记录`);
          this.detectGaps();
          resolve(this.data);
        })
        .on("error", (error) => {
          console.error(`加载数据文件失败: ${error}`);
          reject(error);
        });
    });
  }

  /**
   * 解析CSV行数据
   * @param row 原始CSV行
   * @returns MarketTick | null
   */
  private parseRow(row: RawCsvRow): MarketTick | null {
    try {
      // 简化处理：假设CSV中的时间已经是正确的格式
      // 在实际应用中，可能需要根据具体的时区要求进行转换
      return {
        datetime: row.datetime,
        open: parseFloat(row.open),
        high: parseFloat(row.high),
        low: parseFloat(row.low),
        close: parseFloat(row.close),
        volume: parseInt(row.volume),
      };
    } catch (error) {
      console.warn(`解析行数据失败: ${JSON.stringify(row)}, 错误: ${error}`);
      return null;
    }
  }

  /**
   * 检查是否为有效交易时间
   * @param tick MarketTick数据
   * @returns boolean
   */
  private isValidTradingTime(tick: MarketTick): boolean {
    const time = dayjs(tick.datetime);
    const hour = time.hour();
    const dayOfWeek = time.day();

    // 跳过周末
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return false;
    }

    // 跳过结算时间 (16:00-17:00，根据CSV中的时间格式)
    if (hour >= 16 && hour < 17) {
      return false;
    }

    return true;
  }

  /**
   * 检测时间跳空并标记会话结束
   */
  private detectGaps(): void {
    for (let i = 1; i < this.data.length; i++) {
      const current = this.data[i]!;
      const previous = this.data[i - 1]!;

      const currentTime = dayjs(current.datetime);
      const previousTime = dayjs(previous.datetime);

      // 检查时间间隔是否超过5秒
      const timeDiffSeconds = currentTime.diff(previousTime, "second");

      if (timeDiffSeconds > 5) {
        // 标记前一个tick为会话结束
        previous.isSessionEnd = true;
        // 标记当前tick为新会话开始
        current.isSessionStart = true;

        console.log(`检测到时间跳空: ${previous.datetime} -> ${current.datetime}, 间隔: ${timeDiffSeconds}秒`);
      }
    }
  }

  /**
   * 检测价格跳空
   * @param currentTick 当前tick
   * @param previousTick 前一个tick
   * @returns GapDetection
   */
  static detectPriceGap(currentTick: MarketTick, previousTick: MarketTick): GapDetection {
    const gapSize = Math.abs(currentTick.open - previousTick.close);
    const hasGap = gapSize > 0.25; // 最小跳空阈值
    
    let gapDirection: 'up' | 'down' | null = null;
    if (hasGap) {
      gapDirection = currentTick.open > previousTick.close ? 'up' : 'down';
    }

    return {
      hasGap,
      gapSize,
      gapDirection,
      previousClose: previousTick.close,
      currentOpen: currentTick.open,
    };
  }

  /**
   * 获取加载的数据
   * @returns MarketTick[]
   */
  getData(): MarketTick[] {
    return this.data;
  }

  /**
   * 获取数据统计信息
   */
  getStats() {
    const sessionEnds = this.data.filter(tick => tick.isSessionEnd).length;
    const sessionStarts = this.data.filter(tick => tick.isSessionStart).length;
    
    return {
      totalTicks: this.data.length,
      sessionEnds,
      sessionStarts,
      dateRange: {
        start: this.data[0]?.datetime,
        end: this.data[this.data.length - 1]?.datetime,
      },
    };
  }
}
