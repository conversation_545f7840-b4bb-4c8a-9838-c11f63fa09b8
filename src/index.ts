import { BacktestEngine } from "./backtest/engine.ts";
import { DEFAULT_CONFIG, type AppConfig } from "./types/config.ts";
import chalk from "chalk";
import { readFileSync } from "fs";

/**
 * SingleBar策略回测系统主入口
 */
async function main() {
  console.log(chalk.blue.bold("=== SingleBar策略回测系统 ===\n"));

  try {
    // 加载配置文件
    let config = { ...DEFAULT_CONFIG };

    try {
      const configFile = readFileSync("config.json", "utf-8");
      const userConfig = JSON.parse(configFile);

      // 合并配置
      config = {
        ...config,
        strategy: { ...config.strategy, ...userConfig.strategy },
        backtest: { ...config.backtest, ...userConfig.backtest },
      };

      console.log(chalk.green("已加载 config.json 配置文件"));
    } catch (error) {
      console.log(chalk.yellow("未找到 config.json，使用默认配置"));
    }

    // 显示配置信息
    console.log(chalk.yellow("回测配置:"));
    console.log(`数据文件: ${config.backtest.dataFile}`);
    console.log(`初始资金: $${config.backtest.initialCapital}`);
    console.log(`策略方向: ${config.strategy.strategyDirection}`);
    console.log(`合约倍数: ${config.strategy.contractMultiplier}`);
    console.log(`最大亏损: $${config.strategy.maxLossValue}`);
    console.log(`多头IBS阈值: ${config.strategy.bullIBSTarget}`);
    console.log(`空头IBS阈值: ${config.strategy.bearIBSTarget}`);
    console.log("");

    // 创建回测引擎
    const engine = new BacktestEngine(config);

    // 运行回测
    console.log(chalk.green("开始运行回测..."));
    await engine.run();

    // 获取回测结果
    const results = engine.getResults();

    // 显示结果摘要
    console.log(chalk.blue.bold("\n=== 回测结果摘要 ==="));
    console.log(`总交易次数: ${results.stats.totalTrades}`);
    console.log(`胜率: ${chalk.green(results.stats.winRate.toFixed(2) + '%')}`);
    console.log(`总盈亏: ${results.stats.totalPnl >= 0 ? chalk.green('+') : chalk.red('')}$${results.stats.totalPnl.toFixed(2)}`);
    console.log(`最大盈利: ${chalk.green('+$' + results.stats.maxProfit.toFixed(2))}`);
    console.log(`最大亏损: ${chalk.red('$' + results.stats.maxLoss.toFixed(2))}`);
    console.log(`平均持仓时间: ${results.stats.avgHoldingTime.toFixed(1)}分钟`);

    if (results.stats.totalTrades > 0) {
      const returnRate = (results.stats.totalPnl / config.backtest.initialCapital) * 100;
      console.log(`收益率: ${returnRate >= 0 ? chalk.green('+') : chalk.red('')}${returnRate.toFixed(2)}%`);
    }

    // 显示最近几笔交易
    if (results.trades.length > 0) {
      console.log(chalk.blue.bold("\n=== 最近交易记录 ==="));
      const recentTrades = results.trades.slice(-5);

      console.log("时间\t\t方向\t入场价\t离场价\t盈亏");
      console.log("-".repeat(60));

      recentTrades.forEach(trade => {
        const pnlColor = trade.netPnl >= 0 ? chalk.green : chalk.red;
        const pnlText = trade.netPnl >= 0 ? `+${trade.netPnl.toFixed(2)}` : trade.netPnl.toFixed(2);

        console.log(
          `${trade.entryTime.substring(5, 16)}\t${trade.side}\t${trade.entryPrice}\t${trade.exitPrice}\t${pnlColor(pnlText)}`
        );
      });
    }

    console.log(chalk.blue.bold("\n回测完成！"));

  } catch (error) {
    console.error(chalk.red("回测过程中发生错误:"), error);
    process.exit(1);
  }
}

// 运行主程序
if (import.meta.main) {
  main().catch(console.error);
}