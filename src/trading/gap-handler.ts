import { MarketTick, GapDetection } from "../types/tick.ts";
import { Position, Order, TradeSide } from "../types/trade.ts";
import { DataLoader } from "../data/data-loader.ts";

/**
 * 跳空处理器
 * 专门处理价格跳空情况，调整订单执行和仓位管理
 */
export class GapHandler {
  private gapThreshold: number;
  private minTick: number;

  constructor(gapThreshold: number = 0.25, minTick: number = 0.25) {
    this.gapThreshold = gapThreshold;
    this.minTick = minTick;
  }

  /**
   * 检测价格跳空
   * @param currentTick 当前tick
   * @param previousTick 前一个tick
   * @returns GapDetection
   */
  detectGap(currentTick: MarketTick, previousTick: MarketTick): GapDetection {
    return DataLoader.detectPriceGap(currentTick, previousTick);
  }

  /**
   * 处理跳空开盘的订单执行
   * @param order 待执行订单
   * @param tick 跳空开盘的tick
   * @param gapInfo 跳空信息
   * @returns { shouldFill: boolean, adjustedPrice: number }
   */
  handleGapOrderExecution(
    order: Order,
    tick: MarketTick,
    gapInfo: GapDetection
  ): { shouldFill: boolean; adjustedPrice: number } {
    if (!gapInfo.hasGap) {
      return { shouldFill: true, adjustedPrice: order.price };
    }

    console.log(`处理跳空订单执行: ${order.id}, 跳空方向: ${gapInfo.gapDirection}, 跳空大小: ${gapInfo.gapSize.toFixed(2)}`);

    // 市价单：直接使用跳空后的开盘价
    if (order.type === 'MARKET') {
      return {
        shouldFill: true,
        adjustedPrice: tick.open,
      };
    }

    // 限价单和止损单需要根据跳空方向判断
    return this.handleGapLimitStopOrder(order, tick, gapInfo);
  }

  /**
   * 处理跳空情况下的限价单和止损单
   * @param order 订单
   * @param tick 跳空tick
   * @param gapInfo 跳空信息
   * @returns { shouldFill: boolean, adjustedPrice: number }
   */
  private handleGapLimitStopOrder(
    order: Order,
    tick: MarketTick,
    gapInfo: GapDetection
  ): { shouldFill: boolean; adjustedPrice: number } {
    const isUpGap = gapInfo.gapDirection === 'up';
    const isDownGap = gapInfo.gapDirection === 'down';

    if (order.type === 'LIMIT') {
      return this.handleGapLimitOrder(order, tick, isUpGap, isDownGap, gapInfo);
    } else if (order.type === 'STOP') {
      return this.handleGapStopOrder(order, tick, isUpGap, isDownGap);
    }

    return { shouldFill: false, adjustedPrice: order.price };
  }

  /**
   * 处理跳空限价单
   * @param order 限价单
   * @param tick 跳空tick
   * @param isUpGap 是否向上跳空
   * @param isDownGap 是否向下跳空
   * @param gapInfo 跳空信息
   * @returns { shouldFill: boolean, adjustedPrice: number }
   */
  private handleGapLimitOrder(
    order: Order,
    tick: MarketTick,
    isUpGap: boolean,
    isDownGap: boolean,
    gapInfo: GapDetection
  ): { shouldFill: boolean; adjustedPrice: number } {
    if (order.side === 'LONG') {
      // 买入限价单
      if (isDownGap && tick.open <= order.price) {
        // 向下跳空且开盘价低于限价，以开盘价成交
        return { shouldFill: true, adjustedPrice: tick.open };
      } else if (isUpGap && gapInfo.previousClose <= order.price) {
        // 向上跳空但前收盘价低于限价，以开盘价成交
        return { shouldFill: true, adjustedPrice: tick.open };
      }
    } else {
      // 卖出限价单
      if (isUpGap && tick.open >= order.price) {
        // 向上跳空且开盘价高于限价，以开盘价成交
        return { shouldFill: true, adjustedPrice: tick.open };
      } else if (isDownGap && gapInfo.previousClose >= order.price) {
        // 向下跳空但前收盘价高于限价，以开盘价成交
        return { shouldFill: true, adjustedPrice: tick.open };
      }
    }

    return { shouldFill: false, adjustedPrice: order.price };
  }

  /**
   * 处理跳空止损单
   * @param order 止损单
   * @param tick 跳空tick
   * @param isUpGap 是否向上跳空
   * @param isDownGap 是否向下跳空
   * @returns { shouldFill: boolean, adjustedPrice: number }
   */
  private handleGapStopOrder(
    order: Order,
    tick: MarketTick,
    isUpGap: boolean,
    isDownGap: boolean
  ): { shouldFill: boolean; adjustedPrice: number } {
    if (order.side === 'LONG') {
      // 买入止损单（突破买入）
      if (isUpGap && tick.open >= order.price) {
        return { shouldFill: true, adjustedPrice: tick.open };
      }
    } else {
      // 卖出止损单（止损卖出）
      if (isDownGap && tick.open <= order.price) {
        return { shouldFill: true, adjustedPrice: tick.open };
      }
    }

    return { shouldFill: false, adjustedPrice: order.price };
  }

  /**
   * 调整持仓的止损止盈价格（跳空后）
   * @param position 当前持仓
   * @param tick 跳空tick
   * @param gapInfo 跳空信息
   * @returns { newStopLoss?: number, newTakeProfit?: number }
   */
  adjustPositionLevels(
    position: Position,
    tick: MarketTick,
    gapInfo: GapDetection
  ): { newStopLoss?: number; newTakeProfit?: number } {
    if (!gapInfo.hasGap) return {};

    console.log(`调整持仓止损止盈: ${position.side}, 跳空方向: ${gapInfo.gapDirection}`);

    // 根据跳空方向和持仓方向调整
    if (position.side === 'LONG') {
      return this.adjustLongPositionLevels(position, tick, gapInfo);
    } else {
      return this.adjustShortPositionLevels(position, tick, gapInfo);
    }
  }

  /**
   * 调整多头持仓的止损止盈
   * @param position 多头持仓
   * @param tick 跳空tick
   * @param gapInfo 跳空信息
   * @returns { newStopLoss?: number, newTakeProfit?: number }
   */
  private adjustLongPositionLevels(
    position: Position,
    tick: MarketTick,
    gapInfo: GapDetection
  ): { newStopLoss?: number; newTakeProfit?: number } {
    const result: { newStopLoss?: number; newTakeProfit?: number } = {};

    if (gapInfo.gapDirection === 'down') {
      // 向下跳空，可能需要调整止损
      if (position.stopLoss && tick.open < position.stopLoss) {
        // 跳空开盘价已经低于原止损价，无需调整（会被直接止损）
        console.log("向下跳空已触发止损");
      }
    } else if (gapInfo.gapDirection === 'up') {
      // 向上跳空，可能需要调整止盈或移动止损
      if (position.takeProfit && tick.open > position.takeProfit) {
        // 跳空开盘价已经高于原止盈价，无需调整（会被直接止盈）
        console.log("向上跳空已触发止盈");
      } else {
        // 可以考虑移动止损到更有利位置
        const newStopLoss = Math.max(
          position.stopLoss || 0,
          tick.open - gapInfo.gapSize * 0.5 // 保守调整
        );
        if (newStopLoss > (position.stopLoss || 0)) {
          result.newStopLoss = newStopLoss;
        }
      }
    }

    return result;
  }

  /**
   * 调整空头持仓的止损止盈
   * @param position 空头持仓
   * @param tick 跳空tick
   * @param gapInfo 跳空信息
   * @returns { newStopLoss?: number, newTakeProfit?: number }
   */
  private adjustShortPositionLevels(
    position: Position,
    tick: MarketTick,
    gapInfo: GapDetection
  ): { newStopLoss?: number; newTakeProfit?: number } {
    const result: { newStopLoss?: number; newTakeProfit?: number } = {};

    if (gapInfo.gapDirection === 'up') {
      // 向上跳空，可能需要调整止损
      if (position.stopLoss && tick.open > position.stopLoss) {
        // 跳空开盘价已经高于原止损价，无需调整（会被直接止损）
        console.log("向上跳空已触发止损");
      }
    } else if (gapInfo.gapDirection === 'down') {
      // 向下跳空，可能需要调整止盈或移动止损
      if (position.takeProfit && tick.open < position.takeProfit) {
        // 跳空开盘价已经低于原止盈价，无需调整（会被直接止盈）
        console.log("向下跳空已触发止盈");
      } else {
        // 可以考虑移动止损到更有利位置
        const newStopLoss = Math.min(
          position.stopLoss || Infinity,
          tick.open + gapInfo.gapSize * 0.5 // 保守调整
        );
        if (newStopLoss < (position.stopLoss || Infinity)) {
          result.newStopLoss = newStopLoss;
        }
      }
    }

    return result;
  }

  /**
   * 计算跳空对盈亏的影响
   * @param position 持仓
   * @param gapInfo 跳空信息
   * @param contractMultiplier 合约倍数
   * @returns number 跳空影响的盈亏
   */
  calculateGapImpact(
    position: Position,
    gapInfo: GapDetection,
    contractMultiplier: number
  ): number {
    if (!gapInfo.hasGap) return 0;

    const gapPoints = position.side === 'LONG' 
      ? gapInfo.currentOpen - gapInfo.previousClose
      : gapInfo.previousClose - gapInfo.currentOpen;

    return gapPoints * position.quantity * contractMultiplier;
  }

  /**
   * 检查跳空是否对策略有利
   * @param side 交易方向
   * @param gapInfo 跳空信息
   * @returns boolean
   */
  isGapFavorable(side: TradeSide, gapInfo: GapDetection): boolean {
    if (!gapInfo.hasGap) return false;

    return (side === 'LONG' && gapInfo.gapDirection === 'up') ||
           (side === 'SHORT' && gapInfo.gapDirection === 'down');
  }

  /**
   * 获取跳空统计信息
   * @param gapHistory 跳空历史记录
   */
  getGapStats(gapHistory: GapDetection[]) {
    const totalGaps = gapHistory.length;
    const upGaps = gapHistory.filter(g => g.gapDirection === 'up').length;
    const downGaps = gapHistory.filter(g => g.gapDirection === 'down').length;
    
    const avgGapSize = gapHistory.reduce((sum, g) => sum + g.gapSize, 0) / totalGaps;
    const maxGapSize = Math.max(...gapHistory.map(g => g.gapSize));
    const minGapSize = Math.min(...gapHistory.map(g => g.gapSize));

    return {
      totalGaps,
      upGaps,
      downGaps,
      upGapRate: (upGaps / totalGaps) * 100,
      downGapRate: (downGaps / totalGaps) * 100,
      avgGapSize,
      maxGapSize,
      minGapSize,
    };
  }

  /**
   * 更新跳空阈值
   * @param newThreshold 新的跳空阈值
   */
  updateGapThreshold(newThreshold: number): void {
    this.gapThreshold = newThreshold;
    console.log(`跳空阈值已更新为: ${newThreshold}`);
  }
}
