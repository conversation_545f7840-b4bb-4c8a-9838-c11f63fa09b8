import { Order, OrderStatus, OrderType, TradeSide, TradeSignal } from "../types/trade.ts";
import { MarketTick, GapDetection } from "../types/tick.ts";
import { BacktestConfig } from "../types/config.ts";

/**
 * 订单管理器
 * 负责订单的创建、执行、管理和价格调整
 */
export class OrderManager {
  private orders: Map<string, Order> = new Map();
  private nextOrderId = 1;
  private config: BacktestConfig;

  // 事件回调
  private onOrderFilledCallback?: (order: Order, fillPrice: number) => void;
  private onOrderCancelledCallback?: (order: Order) => void;

  constructor(config: BacktestConfig) {
    this.config = config;
  }

  /**
   * 根据交易信号创建订单
   * @param signal 交易信号
   * @returns Order
   */
  createOrderFromSignal(signal: TradeSignal): Order {
    const orderId = this.generateOrderId();
    const side = signal.type === 'ENTRY_LONG' ? 'LONG' : 'SHORT';
    
    const order: Order = {
      id: orderId,
      type: 'MARKET', // 默认使用市价单
      side,
      quantity: signal.quantity,
      price: signal.price,
      status: 'PENDING',
      createTime: signal.time,
      updateTime: signal.time,
      tag: signal.type,
    };

    this.orders.set(orderId, order);
    console.log(`创建订单: ${order.id} ${order.side} ${order.quantity}手 @ ${order.price}`);
    
    return order;
  }

  /**
   * 创建止损订单
   * @param parentOrderId 主订单ID
   * @param side 交易方向
   * @param quantity 数量
   * @param stopPrice 止损价格
   * @param time 创建时间
   * @returns Order
   */
  createStopLossOrder(
    parentOrderId: string,
    side: TradeSide,
    quantity: number,
    stopPrice: number,
    time: string
  ): Order {
    const orderId = this.generateOrderId();
    
    const order: Order = {
      id: orderId,
      type: 'STOP',
      side: side === 'LONG' ? 'SHORT' : 'LONG', // 止损方向相反
      quantity,
      price: stopPrice,
      status: 'PENDING',
      createTime: time,
      updateTime: time,
      parentOrderId,
      tag: 'STOP_LOSS',
    };

    this.orders.set(orderId, order);
    console.log(`创建止损订单: ${order.id} @ ${stopPrice}`);
    
    return order;
  }

  /**
   * 创建止盈订单
   * @param parentOrderId 主订单ID
   * @param side 交易方向
   * @param quantity 数量
   * @param takeProfitPrice 止盈价格
   * @param time 创建时间
   * @returns Order
   */
  createTakeProfitOrder(
    parentOrderId: string,
    side: TradeSide,
    quantity: number,
    takeProfitPrice: number,
    time: string
  ): Order {
    const orderId = this.generateOrderId();
    
    const order: Order = {
      id: orderId,
      type: 'LIMIT',
      side: side === 'LONG' ? 'SHORT' : 'LONG', // 止盈方向相反
      quantity,
      price: takeProfitPrice,
      status: 'PENDING',
      createTime: time,
      updateTime: time,
      parentOrderId,
      tag: 'TAKE_PROFIT',
    };

    this.orders.set(orderId, order);
    console.log(`创建止盈订单: ${order.id} @ ${takeProfitPrice}`);
    
    return order;
  }

  /**
   * 处理市场tick，检查订单执行
   * @param tick 市场数据
   * @param gapInfo 跳空信息
   * @returns Order[] 被执行的订单
   */
  processTick(tick: MarketTick, gapInfo?: GapDetection): Order[] {
    const filledOrders: Order[] = [];
    
    for (const order of this.orders.values()) {
      if (order.status !== 'PENDING') continue;
      
      const fillPrice = this.checkOrderExecution(order, tick, gapInfo);
      if (fillPrice !== null) {
        this.fillOrder(order, fillPrice, tick.datetime);
        filledOrders.push(order);
      }
    }
    
    return filledOrders;
  }

  /**
   * 检查订单是否应该执行
   * @param order 订单
   * @param tick 市场数据
   * @param gapInfo 跳空信息
   * @returns number | null 成交价格，null表示不成交
   */
  private checkOrderExecution(order: Order, tick: MarketTick, gapInfo?: GapDetection): number | null {
    switch (order.type) {
      case 'MARKET':
        return this.getMarketFillPrice(order, tick, gapInfo);
      
      case 'LIMIT':
        return this.checkLimitOrderFill(order, tick);
      
      case 'STOP':
        return this.checkStopOrderFill(order, tick);
      
      default:
        return null;
    }
  }

  /**
   * 获取市价单成交价格
   * @param order 订单
   * @param tick 市场数据
   * @param gapInfo 跳空信息
   * @returns number
   */
  private getMarketFillPrice(order: Order, tick: MarketTick, gapInfo?: GapDetection): number {
    let fillPrice = tick.open; // 默认使用开盘价

    // 如果有跳空，需要调整成交价格
    if (gapInfo?.hasGap && tick.isSessionStart) {
      console.log(`跳空开盘调整成交价格: 信号价格 ${order.price} -> 实际成交 ${tick.open}`);
      fillPrice = tick.open;
    } else {
      // 正常情况下使用信号价格，但要考虑滑点
      fillPrice = this.applySlippage(order.price, order.side);
    }

    return fillPrice;
  }

  /**
   * 检查限价单是否成交
   * @param order 限价单
   * @param tick 市场数据
   * @returns number | null
   */
  private checkLimitOrderFill(order: Order, tick: MarketTick): number | null {
    if (order.side === 'LONG') {
      // 买入限价单：当前价格 <= 限价
      if (tick.low <= order.price) {
        return Math.min(order.price, tick.open);
      }
    } else {
      // 卖出限价单：当前价格 >= 限价
      if (tick.high >= order.price) {
        return Math.max(order.price, tick.open);
      }
    }
    
    return null;
  }

  /**
   * 检查止损单是否成交
   * @param order 止损单
   * @param tick 市场数据
   * @returns number | null
   */
  private checkStopOrderFill(order: Order, tick: MarketTick): number | null {
    if (order.side === 'LONG') {
      // 买入止损单：当前价格 >= 止损价
      if (tick.high >= order.price) {
        return Math.max(order.price, tick.open);
      }
    } else {
      // 卖出止损单：当前价格 <= 止损价
      if (tick.low <= order.price) {
        return Math.min(order.price, tick.open);
      }
    }
    
    return null;
  }

  /**
   * 应用滑点
   * @param price 原价格
   * @param side 交易方向
   * @returns number 调整后价格
   */
  private applySlippage(price: number, side: TradeSide): number {
    const slippage = this.config.slippage;
    
    if (side === 'LONG') {
      return price + slippage; // 买入时价格上滑
    } else {
      return price - slippage; // 卖出时价格下滑
    }
  }

  /**
   * 执行订单
   * @param order 订单
   * @param fillPrice 成交价格
   * @param fillTime 成交时间
   */
  private fillOrder(order: Order, fillPrice: number, fillTime: string): void {
    order.status = 'FILLED';
    order.fillPrice = fillPrice;
    order.fillTime = fillTime;
    order.updateTime = fillTime;
    
    console.log(`订单成交: ${order.id} ${order.side} ${order.quantity}手 @ ${fillPrice}`);
    
    if (this.onOrderFilledCallback) {
      this.onOrderFilledCallback(order, fillPrice);
    }
  }

  /**
   * 取消订单
   * @param orderId 订单ID
   * @returns boolean 是否成功取消
   */
  cancelOrder(orderId: string): boolean {
    const order = this.orders.get(orderId);
    if (!order || order.status !== 'PENDING') {
      return false;
    }
    
    order.status = 'CANCELLED';
    order.updateTime = new Date().toISOString();
    
    console.log(`订单已取消: ${orderId}`);
    
    if (this.onOrderCancelledCallback) {
      this.onOrderCancelledCallback(order);
    }
    
    return true;
  }

  /**
   * 取消所有挂单
   */
  cancelAllOrders(): void {
    for (const order of this.orders.values()) {
      if (order.status === 'PENDING') {
        this.cancelOrder(order.id);
      }
    }
  }

  /**
   * 取消指定父订单的所有子订单
   * @param parentOrderId 父订单ID
   */
  cancelChildOrders(parentOrderId: string): void {
    for (const order of this.orders.values()) {
      if (order.parentOrderId === parentOrderId && order.status === 'PENDING') {
        this.cancelOrder(order.id);
      }
    }
  }

  /**
   * 生成订单ID
   * @returns string
   */
  private generateOrderId(): string {
    return `O${this.nextOrderId++.toString().padStart(6, '0')}`;
  }

  /**
   * 获取所有订单
   * @returns Order[]
   */
  getAllOrders(): Order[] {
    return Array.from(this.orders.values());
  }

  /**
   * 获取挂单
   * @returns Order[]
   */
  getPendingOrders(): Order[] {
    return Array.from(this.orders.values()).filter(order => order.status === 'PENDING');
  }

  /**
   * 获取已成交订单
   * @returns Order[]
   */
  getFilledOrders(): Order[] {
    return Array.from(this.orders.values()).filter(order => order.status === 'FILLED');
  }

  /**
   * 设置订单成交回调
   * @param callback 回调函数
   */
  onOrderFilled(callback: (order: Order, fillPrice: number) => void): void {
    this.onOrderFilledCallback = callback;
  }

  /**
   * 设置订单取消回调
   * @param callback 回调函数
   */
  onOrderCancelled(callback: (order: Order) => void): void {
    this.onOrderCancelledCallback = callback;
  }

  /**
   * 重置订单管理器
   */
  reset(): void {
    this.orders.clear();
    this.nextOrderId = 1;
    console.log("订单管理器已重置");
  }
}
