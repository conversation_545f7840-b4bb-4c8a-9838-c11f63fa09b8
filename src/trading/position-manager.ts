import { Position, TradeSide, TradeRecord } from "../types/trade.ts";
import { StrategyConfig } from "../types/config.ts";
import { MarketTick } from "../types/tick.ts";

/**
 * 仓位管理器
 * 负责管理持仓状态、计算盈亏、风险控制
 */
export class PositionManager {
  private currentPosition: Position | null = null;
  private config: StrategyConfig;
  private tradeHistory: TradeRecord[] = [];
  private nextTradeId = 1;

  constructor(config: StrategyConfig) {
    this.config = config;
  }

  /**
   * 开仓
   * @param side 交易方向
   * @param quantity 数量
   * @param price 价格
   * @param time 时间
   * @param stopLoss 止损价格
   * @param takeProfit 止盈价格
   * @param gapAdjusted 是否因跳空调整
   * @returns boolean 是否成功开仓
   */
  openPosition(
    side: TradeSide,
    quantity: number,
    price: number,
    time: string,
    stopLoss?: number,
    takeProfit?: number,
    gapAdjusted: boolean = false
  ): boolean {
    if (this.hasPosition()) {
      console.warn("已有持仓，无法开新仓");
      return false;
    }

    this.currentPosition = {
      side,
      quantity,
      avgPrice: price,
      currentPrice: price,
      unrealizedPnl: 0,
      entryTime: time,
      stopLoss,
      takeProfit,
    };

    console.log(`开仓: ${side} ${quantity}手 @ ${price}, 止损: ${stopLoss}, 止盈: ${takeProfit}`);
    return true;
  }

  /**
   * 平仓
   * @param price 平仓价格
   * @param time 平仓时间
   * @param reason 平仓原因
   * @param gapAdjusted 是否因跳空调整
   * @returns TradeRecord | null 交易记录
   */
  closePosition(
    price: number,
    time: string,
    reason: string = "正常平仓",
    gapAdjusted: boolean = false
  ): TradeRecord | null {
    if (!this.currentPosition) {
      console.warn("无持仓，无法平仓");
      return null;
    }

    const position = this.currentPosition;
    
    // 计算盈亏
    const pnlPoints = position.side === 'LONG' 
      ? price - position.avgPrice
      : position.avgPrice - price;
    
    const pnl = pnlPoints * position.quantity * this.config.contractMultiplier;
    
    // 计算手续费（简化处理）
    const commission = this.calculateCommission(position.quantity, position.avgPrice, price);
    const netPnl = pnl - commission;
    
    // 计算持仓时间
    const entryTime = new Date(position.entryTime);
    const exitTime = new Date(time);
    const holdingTimeMinutes = Math.round((exitTime.getTime() - entryTime.getTime()) / (1000 * 60));

    // 创建交易记录
    const tradeRecord: TradeRecord = {
      id: `T${this.nextTradeId.toString().padStart(6, '0')}`,
      entryTime: position.entryTime,
      entryPrice: position.avgPrice,
      exitTime: time,
      exitPrice: price,
      side: position.side,
      quantity: position.quantity,
      pnl,
      pnlPoints,
      commission,
      netPnl,
      gapAdjusted,
      holdingTimeMinutes,
      entryReason: "策略信号",
      exitReason: reason,
    };

    this.tradeHistory.push(tradeRecord);
    this.nextTradeId++;
    
    console.log(`平仓: ${position.side} ${position.quantity}手 @ ${price}, 盈亏: ${pnl.toFixed(2)}, 净盈亏: ${netPnl.toFixed(2)}`);
    
    // 清空持仓
    this.currentPosition = null;
    
    return tradeRecord;
  }

  /**
   * 更新持仓的当前价格和未实现盈亏
   * @param currentPrice 当前价格
   */
  updatePosition(currentPrice: number): void {
    if (!this.currentPosition) return;

    this.currentPosition.currentPrice = currentPrice;
    
    const pnlPoints = this.currentPosition.side === 'LONG'
      ? currentPrice - this.currentPosition.avgPrice
      : this.currentPosition.avgPrice - currentPrice;
    
    this.currentPosition.unrealizedPnl = pnlPoints * this.currentPosition.quantity * this.config.contractMultiplier;
  }

  /**
   * 检查是否触发止损
   * @param currentPrice 当前价格
   * @returns boolean
   */
  checkStopLoss(currentPrice: number): boolean {
    if (!this.currentPosition || !this.currentPosition.stopLoss) return false;

    if (this.currentPosition.side === 'LONG') {
      return currentPrice <= this.currentPosition.stopLoss;
    } else {
      return currentPrice >= this.currentPosition.stopLoss;
    }
  }

  /**
   * 检查是否触发止盈
   * @param currentPrice 当前价格
   * @returns boolean
   */
  checkTakeProfit(currentPrice: number): boolean {
    if (!this.currentPosition || !this.currentPosition.takeProfit) return false;

    if (this.currentPosition.side === 'LONG') {
      return currentPrice >= this.currentPosition.takeProfit;
    } else {
      return currentPrice <= this.currentPosition.takeProfit;
    }
  }

  /**
   * 更新止损价格
   * @param newStopLoss 新的止损价格
   */
  updateStopLoss(newStopLoss: number): void {
    if (this.currentPosition) {
      this.currentPosition.stopLoss = newStopLoss;
      console.log(`更新止损价格: ${newStopLoss}`);
    }
  }

  /**
   * 更新止盈价格
   * @param newTakeProfit 新的止盈价格
   */
  updateTakeProfit(newTakeProfit: number): void {
    if (this.currentPosition) {
      this.currentPosition.takeProfit = newTakeProfit;
      console.log(`更新止盈价格: ${newTakeProfit}`);
    }
  }

  /**
   * 计算手续费
   * @param quantity 数量
   * @param entryPrice 入场价格
   * @param exitPrice 出场价格
   * @returns number 手续费
   */
  private calculateCommission(quantity: number, entryPrice: number, exitPrice: number): number {
    // 简化的手续费计算：按成交金额的比例
    const entryValue = quantity * entryPrice * this.config.contractMultiplier;
    const exitValue = quantity * exitPrice * this.config.contractMultiplier;
    const totalValue = entryValue + exitValue;
    
    // 假设手续费率为0.01%
    return totalValue * 0.0001;
  }

  /**
   * 检查是否有持仓
   * @returns boolean
   */
  hasPosition(): boolean {
    return this.currentPosition !== null;
  }

  /**
   * 获取当前持仓
   * @returns Position | null
   */
  getCurrentPosition(): Position | null {
    return this.currentPosition ? { ...this.currentPosition } : null;
  }

  /**
   * 获取交易历史
   * @returns TradeRecord[]
   */
  getTradeHistory(): TradeRecord[] {
    return [...this.tradeHistory];
  }

  /**
   * 获取最后一笔交易
   * @returns TradeRecord | null
   */
  getLastTrade(): TradeRecord | null {
    return this.tradeHistory.length > 0 
      ? { ...this.tradeHistory[this.tradeHistory.length - 1]! }
      : null;
  }

  /**
   * 获取交易统计
   */
  getTradeStats() {
    const trades = this.tradeHistory;
    const totalTrades = trades.length;
    
    if (totalTrades === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalPnl: 0,
        totalProfit: 0,
        totalLoss: 0,
        avgProfit: 0,
        avgLoss: 0,
        profitLossRatio: 0,
        maxProfit: 0,
        maxLoss: 0,
        avgHoldingTime: 0,
      };
    }

    const winningTrades = trades.filter(t => t.netPnl > 0);
    const losingTrades = trades.filter(t => t.netPnl < 0);
    
    const totalPnl = trades.reduce((sum, t) => sum + t.netPnl, 0);
    const totalProfit = winningTrades.reduce((sum, t) => sum + t.netPnl, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.netPnl, 0));
    
    const avgProfit = winningTrades.length > 0 ? totalProfit / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? totalLoss / losingTrades.length : 0;
    
    const maxProfit = Math.max(...trades.map(t => t.netPnl), 0);
    const maxLoss = Math.min(...trades.map(t => t.netPnl), 0);
    
    const avgHoldingTime = trades.reduce((sum, t) => sum + t.holdingTimeMinutes, 0) / totalTrades;

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: (winningTrades.length / totalTrades) * 100,
      totalPnl,
      totalProfit,
      totalLoss,
      avgProfit,
      avgLoss,
      profitLossRatio: avgLoss > 0 ? avgProfit / avgLoss : 0,
      maxProfit,
      maxLoss,
      avgHoldingTime,
    };
  }

  /**
   * 重置仓位管理器
   */
  reset(): void {
    this.currentPosition = null;
    this.tradeHistory = [];
    this.nextTradeId = 1;
    console.log("仓位管理器已重置");
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  updateConfig(newConfig: StrategyConfig): void {
    this.config = { ...newConfig };
  }
}
