import { Candle5Min, CandleIndicators } from "../types/candle.ts";

/**
 * 技术指标计算器
 * 实现Pine Script中使用的各种技术指标
 */
export class IndicatorCalculator {
  
  /**
   * 计算K线的所有指标
   * @param candle 5分钟K线数据
   * @param config 策略配置
   * @returns CandleIndicators
   */
  static calculateIndicators(
    candle: Candle5Min, 
    config: {
      trendBarJudge: boolean;
      longMinEntityForTrendBar: number;
      shortMinEntityForTrendBar: number;
    }
  ): CandleIndicators {
    const isBullBar = candle.close > candle.open;
    const isBearBar = candle.close < candle.open;
    
    const ibs = this.calculateIBS(candle);
    const bodyRatio = this.calculateBodyRatio(candle);
    const isTrendBar = this.calculateTrendBar(candle, config, isBullBar, isBearBar);
    
    const bodySize = Math.abs(candle.close - candle.open);
    const shadowSize = candle.high - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;

    return {
      isBullBar,
      isBearBar,
      ibs,
      isTrendBar,
      bodyRatio,
      bodySize,
      shadowSize,
      upperShadow,
      lowerShadow,
    };
  }

  /**
   * 计算IBS指标 (Internal Bar Strength)
   * IBS = (close - low) / (high - low) * 100
   * @param candle K线数据
   * @returns number (0-100)
   */
  private static calculateIBS(candle: Candle5Min): number {
    const range = candle.high - candle.low;
    if (range <= 0.0001) return 50; // 避免除零，返回中性值
    
    const ibs = (candle.close - candle.low) / range * 100;
    return Math.round(ibs);
  }

  /**
   * 计算K线实体占比
   * @param candle K线数据
   * @returns number (0-1)
   */
  private static calculateBodyRatio(candle: Candle5Min): number {
    const bodySize = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    
    if (totalRange <= 0.0001) return 0;
    
    return bodySize / totalRange;
  }

  /**
   * 判断是否为趋势K线
   * 基于Pine Script逻辑：实体占整个K线的比例是否超过阈值
   * @param candle K线数据
   * @param config 配置参数
   * @param isBullBar 是否为阳线
   * @param isBearBar 是否为阴线
   * @returns boolean
   */
  private static calculateTrendBar(
    candle: Candle5Min,
    config: {
      trendBarJudge: boolean;
      longMinEntityForTrendBar: number;
      shortMinEntityForTrendBar: number;
    },
    isBullBar: boolean,
    isBearBar: boolean
  ): boolean {
    if (!config.trendBarJudge) return true;

    const bodySize = Math.abs(candle.close - candle.open);
    const totalRange = Math.max(candle.high - candle.low, 0.0001);
    const bodyRatio = bodySize / totalRange;

    let threshold = 0.5; // 默认阈值
    if (isBullBar) {
      threshold = config.longMinEntityForTrendBar;
    } else if (isBearBar) {
      threshold = config.shortMinEntityForTrendBar;
    }

    return bodyRatio > threshold;
  }

  /**
   * 检查多头入场条件
   * @param indicators 指标数据
   * @param candle K线数据
   * @param config 策略配置
   * @returns boolean
   */
  static checkLongCondition(
    indicators: CandleIndicators,
    candle: Candle5Min,
    config: {
      bullIBSTarget: number;
      longMinProfitPoint: number;
      strategyDirection: string;
    }
  ): boolean {
    const directionAllowed = config.strategyDirection === '都做' || config.strategyDirection === '只做多';
    
    return indicators.isBullBar &&
           indicators.ibs > config.bullIBSTarget &&
           indicators.isTrendBar &&
           (candle.high - candle.close) >= config.longMinProfitPoint &&
           directionAllowed;
  }

  /**
   * 检查空头入场条件
   * @param indicators 指标数据
   * @param candle K线数据
   * @param config 策略配置
   * @returns boolean
   */
  static checkShortCondition(
    indicators: CandleIndicators,
    candle: Candle5Min,
    config: {
      bearIBSTarget: number;
      shortMinProfitPoint: number;
      strategyDirection: string;
    }
  ): boolean {
    const directionAllowed = config.strategyDirection === '都做' || config.strategyDirection === '只做空';
    
    return indicators.isBearBar &&
           indicators.ibs < config.bearIBSTarget &&
           indicators.isTrendBar &&
           (candle.close - candle.low) >= config.shortMinProfitPoint &&
           directionAllowed;
  }

  /**
   * 计算仓位大小
   * 基于Pine Script的仓位计算逻辑
   * @param side 交易方向
   * @param candle K线数据
   * @param config 仓位配置
   * @returns number
   */
  static calculatePositionSize(
    side: 'LONG' | 'SHORT',
    candle: Candle5Min,
    config: {
      maxLossValue: number;
      contractMultiplier: number;
    }
  ): number {
    let riskPerContract: number;
    
    if (side === 'LONG') {
      riskPerContract = candle.close - candle.low;
    } else {
      riskPerContract = candle.high - candle.close;
    }

    if (riskPerContract <= 0) return 0;

    const quantity = Math.floor(config.maxLossValue / riskPerContract / config.contractMultiplier);
    return Math.max(0, quantity);
  }

  /**
   * 计算止损止盈价格
   * @param side 交易方向
   * @param candle K线数据
   * @param minTick 最小价格变动单位
   * @returns { stopLoss: number, takeProfit: number }
   */
  static calculateStopLossTakeProfit(
    side: 'LONG' | 'SHORT',
    candle: Candle5Min,
    minTick: number = 0.25
  ): { stopLoss: number; takeProfit: number } {
    if (side === 'LONG') {
      return {
        stopLoss: candle.low - minTick,
        takeProfit: candle.high,
      };
    } else {
      return {
        stopLoss: candle.high + minTick,
        takeProfit: candle.low,
      };
    }
  }

  /**
   * 检查是否为十字星K线
   * @param candle K线数据
   * @param threshold 实体大小阈值
   * @returns boolean
   */
  static isDoji(candle: Candle5Min, threshold: number = 0.1): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    
    if (totalRange <= 0.0001) return true;
    
    return (bodySize / totalRange) < threshold;
  }

  /**
   * 计算K线的波动幅度
   * @param candle K线数据
   * @returns number
   */
  static calculateRange(candle: Candle5Min): number {
    return candle.high - candle.low;
  }

  /**
   * 计算真实波动幅度 (True Range)
   * @param current 当前K线
   * @param previous 前一根K线
   * @returns number
   */
  static calculateTrueRange(current: Candle5Min, previous?: Candle5Min): number {
    if (!previous) {
      return current.high - current.low;
    }

    const tr1 = current.high - current.low;
    const tr2 = Math.abs(current.high - previous.close);
    const tr3 = Math.abs(current.low - previous.close);

    return Math.max(tr1, tr2, tr3);
  }
}
