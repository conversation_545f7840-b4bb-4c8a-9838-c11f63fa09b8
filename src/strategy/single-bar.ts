import { MarketTick } from "../types/tick.ts";
import { Candle5Min } from "../types/candle.ts";
import { TradeSignal, Position, TradeSide } from "../types/trade.ts";
import { StrategyConfig } from "../types/config.ts";
import { CandleBuilder } from "./candle-builder.ts";
import { SignalGenerator } from "./signals.ts";

/**
 * SingleBar策略引擎
 * 实现Pine Script的SingleBar策略逻辑，管理策略状态
 */
export class SingleBarStrategy {
  private candleBuilder: CandleBuilder;
  private signalGenerator: SignalGenerator;
  private config: StrategyConfig;
  private currentPosition: Position | null = null;
  private lastSignal: TradeSignal | null = null;
  private isInitialized = false;

  // 事件回调
  private onSignalCallback?: (signal: TradeSignal) => void;
  private onCandleCompleteCallback?: (candle: Candle5Min) => void;
  private onPositionUpdateCallback?: (position: Position | null) => void;

  constructor(config: StrategyConfig) {
    this.config = config;
    this.candleBuilder = new CandleBuilder();
    this.signalGenerator = new SignalGenerator(config);
  }

  /**
   * 初始化策略
   */
  initialize(): void {
    this.candleBuilder.reset();
    this.currentPosition = null;
    this.lastSignal = null;
    this.isInitialized = true;
    console.log("SingleBar策略已初始化");
  }

  /**
   * 处理实时tick数据
   * @param tick 市场tick数据
   */
  onTick(tick: MarketTick): void {
    if (!this.isInitialized) {
      console.warn("策略未初始化，请先调用 initialize()");
      return;
    }

    // 1. 更新K线状态
    const candleResult = this.candleBuilder.updateWithTick(tick);

    // 2. 实时更新持仓的未实现盈亏
    this.updatePositionPnL(tick.close);

    // 3. 处理跳空情况
    if (candleResult.hasGap && candleResult.gapInfo) {
      this.handleGap(tick, candleResult.gapInfo);
    }

    // 4. K线完成时执行策略逻辑
    if (candleResult.isCompleted) {
      const completedCandle = this.candleBuilder.getLastCompletedCandle();
      if (completedCandle) {
        this.onCandleComplete(completedCandle);
      }
    }

    // 5. 检查是否需要强制平仓
    if (this.shouldForceClose(tick)) {
      this.forceClosePosition(tick);
    }
  }

  /**
   * K线完成时的处理逻辑
   * @param candle 完成的K线
   */
  private onCandleComplete(candle: Candle5Min): void {
    console.log(`K线完成: ${candle.datetime}, OHLCV: ${candle.open}/${candle.high}/${candle.low}/${candle.close}/${candle.volume}`);
    
    // 触发K线完成回调
    if (this.onCandleCompleteCallback) {
      this.onCandleCompleteCallback(candle);
    }

    // 如果没有持仓，尝试生成入场信号
    if (!this.hasPosition()) {
      this.executeStrategyLogic(candle);
    }
  }

  /**
   * 执行策略逻辑
   * @param candle 完成的K线
   */
  private executeStrategyLogic(candle: Candle5Min): void {
    // 生成交易信号
    const signal = this.signalGenerator.generateSignal(
      candle,
      this.hasPosition(),
      this.currentPosition?.side
    );

    if (signal && this.signalGenerator.validateSignal(signal)) {
      console.log(`生成交易信号: ${signal.type} @ ${signal.price}, 数量: ${signal.quantity}`);
      console.log(`信号原因: ${signal.reason}`);
      
      this.lastSignal = signal;
      
      // 触发信号回调
      if (this.onSignalCallback) {
        this.onSignalCallback(signal);
      }
    }
  }

  /**
   * 处理跳空情况
   * @param tick 当前tick
   * @param gapInfo 跳空信息
   */
  private handleGap(tick: MarketTick, gapInfo: any): void {
    console.log(`检测到价格跳空: ${gapInfo.gapDirection} ${gapInfo.gapSize.toFixed(2)}点`);
    
    // 如果有持仓，可能需要调整入场价格或止损价格
    if (this.currentPosition) {
      // 这里可以实现跳空时的仓位调整逻辑
      console.log("持仓中遇到跳空，可能需要调整止损价格");
    }
  }

  /**
   * 更新持仓的未实现盈亏
   * @param currentPrice 当前价格
   */
  private updatePositionPnL(currentPrice: number): void {
    if (!this.currentPosition) return;

    this.currentPosition.currentPrice = currentPrice;
    
    const pnlPerContract = this.currentPosition.side === 'LONG' 
      ? currentPrice - this.currentPosition.avgPrice
      : this.currentPosition.avgPrice - currentPrice;
    
    this.currentPosition.unrealizedPnl = pnlPerContract * this.currentPosition.quantity * this.config.contractMultiplier;
  }

  /**
   * 检查是否应该强制平仓
   * @param tick 当前tick
   * @returns boolean
   */
  private shouldForceClose(tick: MarketTick): boolean {
    // 检查是否为会话结束
    if (tick.isSessionEnd && this.hasPosition()) {
      return true;
    }

    // 检查是否接近收盘时间
    return this.signalGenerator.shouldForceClose(tick.datetime);
  }

  /**
   * 强制平仓
   * @param tick 当前tick
   */
  private forceClosePosition(tick: MarketTick): void {
    if (!this.currentPosition) return;

    const closeSignal = this.signalGenerator.generateCloseSignal(
      tick.datetime,
      tick.close,
      this.currentPosition.side!,
      this.currentPosition.quantity
    );

    console.log(`强制平仓: ${closeSignal.reason}`);

    if (this.onSignalCallback) {
      this.onSignalCallback(closeSignal);
    }
  }

  /**
   * 开仓
   * @param signal 入场信号
   * @param actualPrice 实际成交价格（考虑跳空）
   */
  openPosition(signal: TradeSignal, actualPrice?: number): void {
    if (this.hasPosition()) {
      console.warn("已有持仓，无法开新仓");
      return;
    }

    const entryPrice = actualPrice || signal.price;

    this.currentPosition = {
      side: signal.type === 'ENTRY_LONG' ? 'LONG' : 'SHORT',
      quantity: signal.quantity,
      avgPrice: entryPrice,
      currentPrice: entryPrice,
      unrealizedPnl: 0,
      entryTime: signal.time,
      stopLoss: signal.stopLoss,
      takeProfit: signal.takeProfit,
    };

    console.log(`开仓成功: ${this.currentPosition.side} ${this.currentPosition.quantity}手 @ ${entryPrice}`);

    if (this.onPositionUpdateCallback) {
      this.onPositionUpdateCallback(this.currentPosition);
    }
  }

  /**
   * 平仓
   * @param signal 平仓信号
   * @param actualPrice 实际成交价格
   */
  closePosition(signal: TradeSignal, actualPrice?: number): void {
    if (!this.hasPosition()) {
      console.warn("无持仓，无法平仓");
      return;
    }

    const exitPrice = actualPrice || signal.price;
    console.log(`平仓成功: ${this.currentPosition!.side} ${this.currentPosition!.quantity}手 @ ${exitPrice}`);

    this.currentPosition = null;

    if (this.onPositionUpdateCallback) {
      this.onPositionUpdateCallback(null);
    }
  }

  /**
   * 检查是否有持仓
   * @returns boolean
   */
  hasPosition(): boolean {
    return this.currentPosition !== null;
  }

  /**
   * 获取当前持仓
   * @returns Position | null
   */
  getCurrentPosition(): Position | null {
    return this.currentPosition ? { ...this.currentPosition } : null;
  }

  /**
   * 获取最后一个信号
   * @returns TradeSignal | null
   */
  getLastSignal(): TradeSignal | null {
    return this.lastSignal ? { ...this.lastSignal } : null;
  }

  /**
   * 获取当前K线
   * @returns Candle5Min | null
   */
  getCurrentCandle(): Candle5Min | null {
    return this.candleBuilder.getCurrentCandle();
  }

  /**
   * 获取已完成的K线历史
   * @returns Candle5Min[]
   */
  getCompletedCandles(): Candle5Min[] {
    return this.candleBuilder.getCompletedCandles();
  }

  /**
   * 获取策略统计信息
   */
  getStats() {
    const candleStats = this.candleBuilder.getStats();

    return {
      isInitialized: this.isInitialized,
      hasPosition: this.hasPosition(),
      currentPosition: this.currentPosition,
      lastSignal: this.lastSignal,
      candleStats,
    };
  }

  /**
   * 设置信号回调
   * @param callback 信号回调函数
   */
  onSignal(callback: (signal: TradeSignal) => void): void {
    this.onSignalCallback = callback;
  }

  /**
   * 设置K线完成回调
   * @param callback K线完成回调函数
   */
  onCandleComplete(callback: (candle: Candle5Min) => void): void {
    this.onCandleCompleteCallback = callback;
  }

  /**
   * 设置持仓更新回调
   * @param callback 持仓更新回调函数
   */
  onPositionUpdate(callback: (position: Position | null) => void): void {
    this.onPositionUpdateCallback = callback;
  }

  /**
   * 更新策略配置
   * @param newConfig 新配置
   */
  updateConfig(newConfig: StrategyConfig): void {
    this.config = { ...newConfig };
    this.signalGenerator.updateConfig(newConfig);
    console.log("策略配置已更新");
  }

  /**
   * 获取策略配置
   * @returns StrategyConfig
   */
  getConfig(): StrategyConfig {
    return { ...this.config };
  }

  /**
   * 重置策略状态
   */
  reset(): void {
    this.candleBuilder.reset();
    this.currentPosition = null;
    this.lastSignal = null;
    console.log("策略状态已重置");
  }
}
