import { Candle5Min, CandleIndicators } from "../types/candle.ts";
import { TradeSignal, TradeSide } from "../types/trade.ts";
import { StrategyConfig } from "../types/config.ts";
import { IndicatorCalculator } from "./indicators.ts";

/**
 * 交易信号生成器
 * 基于Pine Script策略逻辑生成买卖信号
 */
export class SignalGenerator {
  private config: StrategyConfig;

  constructor(config: StrategyConfig) {
    this.config = config;
  }

  /**
   * 分析K线并生成交易信号
   * @param candle 完成的5分钟K线
   * @param hasPosition 当前是否有持仓
   * @param currentSide 当前持仓方向
   * @returns TradeSignal | null
   */
  generateSignal(
    candle: Candle5Min,
    hasPosition: boolean = false,
    currentSide?: TradeSide
  ): TradeSignal | null {
    // 如果有持仓，不生成新的入场信号
    if (hasPosition) {
      return null;
    }

    // 计算技术指标
    const indicators = IndicatorCalculator.calculateIndicators(candle, {
      trendBarJudge: this.config.trendBarJudge,
      longMinEntityForTrendBar: this.config.longMinEntityForTrendBar,
      shortMinEntityForTrendBar: this.config.shortMinEntityForTrendBar,
    });

    // 调试信息：显示指标计算结果
    console.log(`指标计算 ${candle.datetime}: IBS=${indicators.ibs}, 阳线=${indicators.isBullBar}, 阴线=${indicators.isBearBar}, 趋势K=${indicators.isTrendBar}, 实体比例=${(indicators.bodyRatio * 100).toFixed(1)}%`);

    // 检查多头信号
    const longSignal = this.checkLongSignal(candle, indicators);
    if (longSignal) {
      return longSignal;
    }

    // 检查空头信号
    const shortSignal = this.checkShortSignal(candle, indicators);
    if (shortSignal) {
      return shortSignal;
    }

    return null;
  }

  /**
   * 检查多头入场信号
   * @param candle K线数据
   * @param indicators 技术指标
   * @returns TradeSignal | null
   */
  private checkLongSignal(candle: Candle5Min, indicators: CandleIndicators): TradeSignal | null {
    const isLongCondition = IndicatorCalculator.checkLongCondition(indicators, candle, {
      bullIBSTarget: this.config.bullIBSTarget,
      longMinProfitPoint: this.config.longMinProfitPoint,
      strategyDirection: this.config.strategyDirection,
    });

    if (!isLongCondition) return null;

    // 计算仓位大小
    const quantity = IndicatorCalculator.calculatePositionSize('LONG', candle, {
      maxLossValue: this.config.maxLossValue,
      contractMultiplier: this.config.contractMultiplier,
    });

    if (quantity <= 1) return null; // Pine Script中的条件：qty > 1

    // 计算止损止盈
    const { stopLoss, takeProfit } = IndicatorCalculator.calculateStopLossTakeProfit('LONG', candle);

    return {
      type: 'ENTRY_LONG',
      time: candle.datetime,
      price: candle.close, // 使用收盘价作为信号价格
      quantity,
      stopLoss,
      takeProfit,
      strength: this.calculateSignalStrength(indicators, 'LONG'),
      reason: this.buildSignalReason(indicators, 'LONG'),
      indicators: {
        ibs: indicators.ibs,
        bodyRatio: indicators.bodyRatio,
        profitPotential: candle.high - candle.close,
      },
    };
  }

  /**
   * 检查空头入场信号
   * @param candle K线数据
   * @param indicators 技术指标
   * @returns TradeSignal | null
   */
  private checkShortSignal(candle: Candle5Min, indicators: CandleIndicators): TradeSignal | null {
    const isShortCondition = IndicatorCalculator.checkShortCondition(indicators, candle, {
      bearIBSTarget: this.config.bearIBSTarget,
      shortMinProfitPoint: this.config.shortMinProfitPoint,
      strategyDirection: this.config.strategyDirection,
    });

    if (!isShortCondition) return null;

    // 计算仓位大小
    const quantity = IndicatorCalculator.calculatePositionSize('SHORT', candle, {
      maxLossValue: this.config.maxLossValue,
      contractMultiplier: this.config.contractMultiplier,
    });

    if (quantity <= 1) return null; // Pine Script中的条件：qty > 1

    // 计算止损止盈
    const { stopLoss, takeProfit } = IndicatorCalculator.calculateStopLossTakeProfit('SHORT', candle);

    return {
      type: 'ENTRY_SHORT',
      time: candle.datetime,
      price: candle.close, // 使用收盘价作为信号价格
      quantity,
      stopLoss,
      takeProfit,
      strength: this.calculateSignalStrength(indicators, 'SHORT'),
      reason: this.buildSignalReason(indicators, 'SHORT'),
      indicators: {
        ibs: indicators.ibs,
        bodyRatio: indicators.bodyRatio,
        profitPotential: candle.close - candle.low,
      },
    };
  }

  /**
   * 生成收盘平仓信号
   * @param currentTime 当前时间
   * @param currentPrice 当前价格
   * @param side 持仓方向
   * @param quantity 持仓数量
   * @returns TradeSignal
   */
  generateCloseSignal(
    currentTime: string,
    currentPrice: number,
    side: TradeSide,
    quantity: number
  ): TradeSignal {
    return {
      type: 'CLOSE_ALL',
      time: currentTime,
      price: currentPrice,
      quantity,
      strength: 1.0,
      reason: '收盘前强制平仓，避免隔夜跳空',
      indicators: {},
    };
  }

  /**
   * 计算信号强度
   * @param indicators 技术指标
   * @param side 交易方向
   * @returns number (0-1)
   */
  private calculateSignalStrength(indicators: CandleIndicators, side: TradeSide): number {
    let strength = 0.5; // 基础强度

    if (side === 'LONG') {
      // IBS越高，多头信号越强
      const ibsStrength = (indicators.ibs - this.config.bullIBSTarget) / (100 - this.config.bullIBSTarget);
      strength += ibsStrength * 0.3;
    } else {
      // IBS越低，空头信号越强
      const ibsStrength = (this.config.bearIBSTarget - indicators.ibs) / this.config.bearIBSTarget;
      strength += ibsStrength * 0.3;
    }

    // 趋势K线增加强度
    if (indicators.isTrendBar) {
      strength += indicators.bodyRatio * 0.2;
    }

    return Math.min(1.0, Math.max(0.0, strength));
  }

  /**
   * 构建信号原因描述
   * @param indicators 技术指标
   * @param side 交易方向
   * @returns string
   */
  private buildSignalReason(indicators: CandleIndicators, side: TradeSide): string {
    const reasons: string[] = [];

    if (side === 'LONG') {
      reasons.push('阳线');
      reasons.push(`IBS=${indicators.ibs}>${this.config.bullIBSTarget}`);
    } else {
      reasons.push('阴线');
      reasons.push(`IBS=${indicators.ibs}<${this.config.bearIBSTarget}`);
    }

    if (indicators.isTrendBar) {
      reasons.push(`趋势K(实体比例=${(indicators.bodyRatio * 100).toFixed(1)}%)`);
    }

    reasons.push('利润空间充足');

    return reasons.join(' + ');
  }

  /**
   * 检查是否应该强制平仓（收盘前）
   * @param currentTime 当前时间
   * @returns boolean
   */
  shouldForceClose(currentTime: string): boolean {
    // 这里可以实现具体的收盘时间判断逻辑
    // 例如：检查是否接近交易日结束
    const time = new Date(currentTime);
    const hour = time.getUTCHours();
    
    // 假设在15:55-16:00之间强制平仓（芝加哥时间）
    return hour === 15 && time.getUTCMinutes() >= 55;
  }

  /**
   * 更新策略配置
   * @param newConfig 新的策略配置
   */
  updateConfig(newConfig: StrategyConfig): void {
    this.config = { ...newConfig };
  }

  /**
   * 获取当前策略配置
   * @returns StrategyConfig
   */
  getConfig(): StrategyConfig {
    return { ...this.config };
  }

  /**
   * 验证信号的有效性
   * @param signal 交易信号
   * @returns boolean
   */
  validateSignal(signal: TradeSignal): boolean {
    // 检查基本字段
    if (!signal.time || !signal.price || signal.quantity <= 0) {
      return false;
    }

    // 检查价格合理性
    if (signal.price <= 0) {
      return false;
    }

    // 检查止损止盈设置
    if (signal.type.includes('ENTRY')) {
      if (!signal.stopLoss || !signal.takeProfit) {
        return false;
      }

      // 检查止损止盈的合理性
      if (signal.type === 'ENTRY_LONG') {
        return signal.stopLoss < signal.price && signal.takeProfit > signal.price;
      } else if (signal.type === 'ENTRY_SHORT') {
        return signal.stopLoss > signal.price && signal.takeProfit < signal.price;
      }
    }

    return true;
  }
}
