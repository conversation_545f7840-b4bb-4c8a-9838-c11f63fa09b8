import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { MarketTick, GapDetection } from "../types/tick.ts";
import { Candle5Min, CandleUpdateResult, TimeWindow } from "../types/candle.ts";
import { DataLoader } from "../data/data-loader.ts";

dayjs.extend(utc);

/**
 * 动态K线构建器
 * 接收5秒级实时数据，动态构建和维护5分钟K线
 */
export class CandleBuilder {
  private currentCandle: Candle5Min | null = null;
  private completedCandles: Candle5Min[] = [];
  private currentWindow: TimeWindow | null = null;
  private previousTick: MarketTick | null = null;

  constructor() {}

  /**
   * 使用新的tick数据更新K线
   * @param tick 新的市场数据
   * @returns CandleUpdateResult
   */
  updateWithTick(tick: MarketTick): CandleUpdateResult {
    const tickTime = dayjs.utc(tick.datetime);
    const newWindow = this.calculateTimeWindow(tickTime);
    
    let isCompleted = false;
    let isNewCandle = false;
    let hasGap = false;
    let gapInfo: GapDetection | undefined;

    // 检测价格跳空
    if (this.previousTick && tick.isSessionStart) {
      gapInfo = DataLoader.detectPriceGap(tick, this.previousTick);
      hasGap = gapInfo.hasGap;
    }

    // 检查是否需要开始新的K线
    if (!this.currentCandle || !this.currentWindow || 
        !this.isInSameWindow(newWindow, this.currentWindow)) {
      
      // 完成当前K线
      if (this.currentCandle) {
        this.currentCandle.isCompleted = true;
        this.completedCandles.push({ ...this.currentCandle });
        isCompleted = true;
      }

      // 开始新K线
      this.currentCandle = this.createNewCandle(tick, newWindow);
      this.currentWindow = newWindow;
      isNewCandle = true;
    } else {
      // 更新当前K线
      this.updateCurrentCandle(tick);
    }

    this.previousTick = tick;

    return {
      currentCandle: { ...this.currentCandle },
      isCompleted,
      isNewCandle,
      hasGap,
      gapInfo,
    };
  }

  /**
   * 计算时间窗口
   * @param time dayjs时间对象
   * @returns TimeWindow
   */
  private calculateTimeWindow(time: dayjs.Dayjs): TimeWindow {
    // 计算5分钟窗口的开始时间（向下取整到5分钟边界）
    const minutes = time.minute();
    const windowStartMinute = Math.floor(minutes / 5) * 5;
    
    const startTime = time
      .minute(windowStartMinute)
      .second(0)
      .millisecond(0);
    
    const endTime = startTime.add(5, 'minute');

    return {
      startTime: startTime.toDate(),
      endTime: endTime.toDate(),
      windowSizeMinutes: 5,
    };
  }

  /**
   * 检查两个时间窗口是否相同
   * @param window1 时间窗口1
   * @param window2 时间窗口2
   * @returns boolean
   */
  private isInSameWindow(window1: TimeWindow, window2: TimeWindow): boolean {
    return dayjs(window1.startTime).isSame(dayjs(window2.startTime));
  }

  /**
   * 创建新的K线
   * @param tick 初始tick数据
   * @param window 时间窗口
   * @returns Candle5Min
   */
  private createNewCandle(tick: MarketTick, window: TimeWindow): Candle5Min {
    return {
      datetime: dayjs(window.startTime).format("YYYY-MM-DD HH:mm:ss"),
      open: tick.open,
      high: tick.high,
      low: tick.low,
      close: tick.close,
      volume: tick.volume,
      isCompleted: false,
      isSessionEnd: tick.isSessionEnd,
      tickCount: 1,
    };
  }

  /**
   * 更新当前K线
   * @param tick 新的tick数据
   */
  private updateCurrentCandle(tick: MarketTick): void {
    if (!this.currentCandle) return;

    // 更新OHLCV
    this.currentCandle.high = Math.max(this.currentCandle.high, tick.high);
    this.currentCandle.low = Math.min(this.currentCandle.low, tick.low);
    this.currentCandle.close = tick.close;
    this.currentCandle.volume += tick.volume;
    this.currentCandle.tickCount++;
    
    // 更新会话结束标记
    if (tick.isSessionEnd) {
      this.currentCandle.isSessionEnd = true;
    }
  }

  /**
   * 强制完成当前K线
   * @returns Candle5Min | null
   */
  forceCompleteCurrentCandle(): Candle5Min | null {
    if (!this.currentCandle) return null;

    this.currentCandle.isCompleted = true;
    const completedCandle = { ...this.currentCandle };
    this.completedCandles.push(completedCandle);
    
    this.currentCandle = null;
    this.currentWindow = null;
    
    return completedCandle;
  }

  /**
   * 获取当前正在构建的K线
   * @returns Candle5Min | null
   */
  getCurrentCandle(): Candle5Min | null {
    return this.currentCandle ? { ...this.currentCandle } : null;
  }

  /**
   * 获取已完成的K线历史
   * @returns Candle5Min[]
   */
  getCompletedCandles(): Candle5Min[] {
    return [...this.completedCandles];
  }

  /**
   * 获取最近N根已完成的K线
   * @param count 数量
   * @returns Candle5Min[]
   */
  getRecentCandles(count: number): Candle5Min[] {
    const startIndex = Math.max(0, this.completedCandles.length - count);
    return this.completedCandles.slice(startIndex);
  }

  /**
   * 获取最后一根已完成的K线
   * @returns Candle5Min | null
   */
  getLastCompletedCandle(): Candle5Min | null {
    return this.completedCandles.length > 0 
      ? { ...this.completedCandles[this.completedCandles.length - 1]! }
      : null;
  }

  /**
   * 获取K线统计信息
   */
  getStats() {
    return {
      completedCandles: this.completedCandles.length,
      currentCandleTickCount: this.currentCandle?.tickCount || 0,
      hasCurrentCandle: this.currentCandle !== null,
      currentWindow: this.currentWindow,
    };
  }

  /**
   * 重置构建器状态
   */
  reset(): void {
    this.currentCandle = null;
    this.completedCandles = [];
    this.currentWindow = null;
    this.previousTick = null;
  }

  /**
   * 检查当前K线是否应该完成（基于时间）
   * @param currentTime 当前时间
   * @returns boolean
   */
  shouldCompleteCandle(currentTime: dayjs.Dayjs): boolean {
    if (!this.currentWindow) return false;
    
    return currentTime.isAfter(dayjs(this.currentWindow.endTime));
  }
}
