/**
 * 5秒级实时市场数据类型
 * 模拟WebSocket推送的实时tick数据
 */
export interface MarketTick {
  /** 时间戳（芝加哥时区格式：2024-03-15 09:30:00） */
  datetime: string;
  /** 开盘价 */
  open: number;
  /** 最高价 */
  high: number;
  /** 最低价 */
  low: number;
  /** 收盘价 */
  close: number;
  /** 成交量 */
  volume: number;
  /** 是否为会话最后一个tick（用于标记跳空） */
  isSessionEnd?: boolean;
  /** 是否为新会话开始（用于检测跳空） */
  isSessionStart?: boolean;
}

/**
 * 原始CSV数据行类型
 */
export interface RawCsvRow {
  datetime: string;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
}

/**
 * 数据流状态
 */
export interface StreamState {
  /** 当前推送的数据索引 */
  currentIndex: number;
  /** 总数据量 */
  totalCount: number;
  /** 是否已完成 */
  isCompleted: boolean;
  /** 当前推送速度（毫秒） */
  intervalMs: number;
}

/**
 * 时间窗口信息
 */
export interface TimeWindow {
  /** 窗口开始时间 */
  startTime: Date;
  /** 窗口结束时间 */
  endTime: Date;
  /** 窗口大小（分钟） */
  windowSizeMinutes: number;
}

/**
 * 跳空检测结果
 */
export interface GapDetection {
  /** 是否存在跳空 */
  hasGap: boolean;
  /** 跳空大小（点数） */
  gapSize: number;
  /** 跳空方向：'up' | 'down' */
  gapDirection: 'up' | 'down' | null;
  /** 前一个收盘价 */
  previousClose: number;
  /** 当前开盘价 */
  currentOpen: number;
}
