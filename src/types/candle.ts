/**
 * 5分钟K线数据类型
 */
export interface Candle5Min {
  /** 时间戳（K线开始时间） */
  datetime: string;
  /** 开盘价 */
  open: number;
  /** 最高价 */
  high: number;
  /** 最低价 */
  low: number;
  /** 收盘价 */
  close: number;
  /** 成交量 */
  volume: number;
  /** K线是否已完成 */
  isCompleted: boolean;
  /** 是否为会话最后一根K线 */
  isSessionEnd?: boolean;
  /** 包含的tick数量 */
  tickCount: number;
}

/**
 * K线构建状态
 */
export interface CandleBuildState {
  /** 当前正在构建的K线 */
  currentCandle: Candle5Min | null;
  /** 已完成的K线历史 */
  completedCandles: Candle5Min[];
  /** 当前时间窗口 */
  currentWindow: TimeWindow | null;
  /** 是否有新完成的K线 */
  hasNewCompletedCandle: boolean;
}

/**
 * K线更新结果
 */
export interface CandleUpdateResult {
  /** 当前K线状态 */
  currentCandle: Candle5Min;
  /** K线是否刚刚完成 */
  isCompleted: boolean;
  /** 是否开始了新的K线 */
  isNewCandle: boolean;
  /** 是否检测到跳空 */
  hasGap: boolean;
  /** 跳空信息 */
  gapInfo?: GapDetection;
}

/**
 * K线指标数据
 */
export interface CandleIndicators {
  /** 是否为阳线 */
  isBullBar: boolean;
  /** 是否为阴线 */
  isBearBar: boolean;
  /** IBS指标值 (0-100) */
  ibs: number;
  /** 是否为趋势K线 */
  isTrendBar: boolean;
  /** 实体占比 */
  bodyRatio: number;
  /** K线实体大小 */
  bodySize: number;
  /** K线影线大小 */
  shadowSize: number;
  /** 上影线大小 */
  upperShadow: number;
  /** 下影线大小 */
  lowerShadow: number;
}

/**
 * 时间窗口信息（从tick.ts导入）
 */
export interface TimeWindow {
  /** 窗口开始时间 */
  startTime: Date;
  /** 窗口结束时间 */
  endTime: Date;
  /** 窗口大小（分钟） */
  windowSizeMinutes: number;
}

/**
 * 跳空检测结果（从tick.ts导入）
 */
export interface GapDetection {
  /** 是否存在跳空 */
  hasGap: boolean;
  /** 跳空大小（点数） */
  gapSize: number;
  /** 跳空方向：'up' | 'down' */
  gapDirection: 'up' | 'down' | null;
  /** 前一个收盘价 */
  previousClose: number;
  /** 当前开盘价 */
  currentOpen: number;
}

/**
 * K线统计信息
 */
export interface CandleStats {
  /** 总K线数量 */
  totalCandles: number;
  /** 阳线数量 */
  bullCandles: number;
  /** 阴线数量 */
  bearCandles: number;
  /** 十字星数量 */
  dojiCandles: number;
  /** 平均成交量 */
  avgVolume: number;
  /** 平均波动幅度 */
  avgRange: number;
}
