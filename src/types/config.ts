/**
 * 策略方向配置
 */
export type StrategyDirection = '都做' | '只做空' | '只做多';

/**
 * 策略配置
 */
export interface StrategyConfig {
  /** 策略方向 */
  strategyDirection: StrategyDirection;
  /** 合约倍数 */
  contractMultiplier: number;
  /** 最大单仓亏损 */
  maxLossValue: number;
  /** 多头最小利润点数 */
  longMinProfitPoint: number;
  /** 空头最小利润点数 */
  shortMinProfitPoint: number;
  /** 多头信号最低IBS值 */
  bullIBSTarget: number;
  /** 空头信号最大IBS值 */
  bearIBSTarget: number;
  /** 是否开启趋势K判断 */
  trendBarJudge: boolean;
  /** 多头趋势K实体最小比例 */
  longMinEntityForTrendBar: number;
  /** 空头趋势K实体最小比例 */
  shortMinEntityForTrendBar: number;
}

/**
 * 回测配置
 */
export interface BacktestConfig {
  /** 初始资金 */
  initialCapital: number;
  /** 数据文件路径 */
  dataFile: string;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 手续费率 */
  commissionRate: number;
  /** 滑点（点数） */
  slippage: number;
  /** 最小价格变动单位 */
  minTick: number;
}

/**
 * 数据流配置
 */
export interface StreamConfig {
  /** 推送间隔（毫秒） */
  intervalMs: number;
  /** 是否快速模式（用于回测） */
  fastMode: boolean;
  /** 快速模式倍速 */
  speedMultiplier: number;
  /** 是否跳过非交易时间 */
  skipNonTradingHours: boolean;
  /** 交易开始时间 */
  tradingStartHour: number;
  /** 交易结束时间 */
  tradingEndHour: number;
  /** 结算开始时间 */
  settlementStartHour: number;
  /** 结算结束时间 */
  settlementEndHour: number;
}

/**
 * 输出配置
 */
export interface OutputConfig {
  /** 结果输出目录 */
  outputDir: string;
  /** 是否生成CSV报告 */
  generateCsv: boolean;
  /** 是否生成Markdown报告 */
  generateMarkdown: boolean;
  /** 是否显示命令行图表 */
  showChart: boolean;
  /** 是否显示实时进度 */
  showProgress: boolean;
  /** 报告文件名前缀 */
  reportPrefix: string;
}

/**
 * 完整应用配置
 */
export interface AppConfig {
  /** 策略配置 */
  strategy: StrategyConfig;
  /** 回测配置 */
  backtest: BacktestConfig;
  /** 数据流配置 */
  stream: StreamConfig;
  /** 输出配置 */
  output: OutputConfig;
}

/**
 * 默认配置
 */
export const DEFAULT_CONFIG: AppConfig = {
  strategy: {
    strategyDirection: '都做',
    contractMultiplier: 5,
    maxLossValue: 500,
    longMinProfitPoint: 1.25,
    shortMinProfitPoint: 1.25,
    bullIBSTarget: 70,
    bearIBSTarget: 48,
    trendBarJudge: false,
    longMinEntityForTrendBar: 0.5,
    shortMinEntityForTrendBar: 0.5,
  },
  backtest: {
    initialCapital: 10000,
    dataFile: 'history_data/MESH4-20240315.csv',
    commissionRate: 0.0001,
    slippage: 0.25,
    minTick: 0.25,
  },
  stream: {
    intervalMs: 100, // 快速回测模式
    fastMode: true,
    speedMultiplier: 50,
    skipNonTradingHours: true,
    tradingStartHour: 17, // 17:00 UTC (芝加哥时间)
    tradingEndHour: 16,   // 16:00 UTC (芝加哥时间)
    settlementStartHour: 16,
    settlementEndHour: 17,
  },
  output: {
    outputDir: 'result',
    generateCsv: true,
    generateMarkdown: true,
    showChart: true,
    showProgress: true,
    reportPrefix: 'singlebar',
  },
};

/**
 * 环境配置
 */
export interface EnvironmentConfig {
  /** 是否为开发模式 */
  isDevelopment: boolean;
  /** 日志级别 */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  /** 是否启用详细日志 */
  verbose: boolean;
}
