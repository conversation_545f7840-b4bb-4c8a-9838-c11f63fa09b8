/**
 * 交易方向
 */
export type TradeSide = 'LONG' | 'SHORT';

/**
 * 订单状态
 */
export type OrderStatus = 'PENDING' | 'FILLED' | 'CANCELLED' | 'REJECTED';

/**
 * 订单类型
 */
export type OrderType = 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';

/**
 * 交易记录
 */
export interface TradeRecord {
  /** 交易ID */
  id: string;
  /** 入场时间 */
  entryTime: string;
  /** 入场价格 */
  entryPrice: number;
  /** 离场时间 */
  exitTime: string;
  /** 离场价格 */
  exitPrice: number;
  /** 交易方向 */
  side: TradeSide;
  /** 交易数量 */
  quantity: number;
  /** 盈亏金额 */
  pnl: number;
  /** 盈亏点数 */
  pnlPoints: number;
  /** 手续费 */
  commission: number;
  /** 净盈亏 */
  netPnl: number;
  /** 是否因跳空调整价格 */
  gapAdjusted: boolean;
  /** 持仓时间（分钟） */
  holdingTimeMinutes: number;
  /** 入场原因 */
  entryReason: string;
  /** 离场原因 */
  exitReason: string;
}

/**
 * 订单信息
 */
export interface Order {
  /** 订单ID */
  id: string;
  /** 订单类型 */
  type: OrderType;
  /** 交易方向 */
  side: TradeSide;
  /** 数量 */
  quantity: number;
  /** 价格 */
  price: number;
  /** 订单状态 */
  status: OrderStatus;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 成交价格 */
  fillPrice?: number;
  /** 成交时间 */
  fillTime?: string;
  /** 关联的主订单ID（用于止损止盈订单） */
  parentOrderId?: string;
  /** 订单标签 */
  tag?: string;
}

/**
 * 持仓信息
 */
export interface Position {
  /** 持仓方向 */
  side: TradeSide | null;
  /** 持仓数量 */
  quantity: number;
  /** 平均成本价 */
  avgPrice: number;
  /** 当前市价 */
  currentPrice: number;
  /** 未实现盈亏 */
  unrealizedPnl: number;
  /** 入场时间 */
  entryTime: string;
  /** 止损价格 */
  stopLoss?: number;
  /** 止盈价格 */
  takeProfit?: number;
  /** 止损订单ID */
  stopLossOrderId?: string;
  /** 止盈订单ID */
  takeProfitOrderId?: string;
}

/**
 * 交易信号
 */
export interface TradeSignal {
  /** 信号类型 */
  type: 'ENTRY_LONG' | 'ENTRY_SHORT' | 'EXIT_LONG' | 'EXIT_SHORT' | 'CLOSE_ALL';
  /** 信号时间 */
  time: string;
  /** 信号价格 */
  price: number;
  /** 建议数量 */
  quantity: number;
  /** 止损价格 */
  stopLoss?: number;
  /** 止盈价格 */
  takeProfit?: number;
  /** 信号强度 (0-1) */
  strength: number;
  /** 信号原因 */
  reason: string;
  /** 相关指标值 */
  indicators?: Record<string, number>;
}

/**
 * 仓位管理配置
 */
export interface PositionConfig {
  /** 合约倍数 */
  contractMultiplier: number;
  /** 最大单仓亏损 */
  maxLossValue: number;
  /** 多头最小利润点数 */
  longMinProfitPoint: number;
  /** 空头最小利润点数 */
  shortMinProfitPoint: number;
  /** 最小交易数量 */
  minQuantity: number;
  /** 最大交易数量 */
  maxQuantity: number;
}

/**
 * 交易统计
 */
export interface TradeStats {
  /** 总交易次数 */
  totalTrades: number;
  /** 盈利交易次数 */
  winningTrades: number;
  /** 亏损交易次数 */
  losingTrades: number;
  /** 胜率 */
  winRate: number;
  /** 总盈亏 */
  totalPnl: number;
  /** 总盈利 */
  totalProfit: number;
  /** 总亏损 */
  totalLoss: number;
  /** 平均盈利 */
  avgProfit: number;
  /** 平均亏损 */
  avgLoss: number;
  /** 盈亏比 */
  profitLossRatio: number;
  /** 最大单笔盈利 */
  maxProfit: number;
  /** 最大单笔亏损 */
  maxLoss: number;
  /** 连续盈利次数 */
  consecutiveWins: number;
  /** 连续亏损次数 */
  consecutiveLosses: number;
  /** 平均持仓时间（分钟） */
  avgHoldingTime: number;
}
