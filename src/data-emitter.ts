import csvParser from "csv-parser";
import { createReadStream } from "fs-extra";
import dayjs from "dayjs";

interface Candle {
  datetime: string;
  open: number;
  high: number;
  low: number;
  close: number;
  isLast?: boolean;
}

export class DataEmitter {
  private data: Candle[] = [];

  constructor() {}

  async loadData(dataPath: string) {
    return new Promise<void>((resolve, reject) => {
      this.data = [];
      createReadStream(dataPath)
        .pipe(csvParser())
        .on("data", (data) => {
          // 使用dayjs判断当前data和上一个data是否存在时间跳空（时间应该按照5s递进，如果没有就是跳空），如果跳空将上一个data标记为isLast
          if (this.data.length > 0) {
            const lastData = this.data[this.data.length - 1];
            const currentData = data as Candle;
            if (lastData) {
              const lastDataTime = dayjs(lastData.datetime);
              const currentDataTime = dayjs(currentData.datetime);
              if (currentDataTime.diff(lastDataTime, "second") > 5) {
                lastData.isLast = true;
              }
            }
          }
          this.data.push(data);
        })
        .on("end", () => {
          resolve();
        })
        .on("error", (error) => {
          console.log(error);
          reject(error);
        });
    });
  }
}
