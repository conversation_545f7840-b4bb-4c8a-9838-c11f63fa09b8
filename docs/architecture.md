# SingleBar策略回测系统架构设计

## 概述

本系统基于Pine Script的SingleBar策略，使用TypeScript实现真实交易环境的模拟回测。核心特点是通过RxJS模拟WebSocket实时数据推送，策略内部动态构建K线并实时响应市场变化。

## 重新设计的数据流架构

### 核心理念

不同于传统的预聚合数据方式，本系统模拟真实交易环境：

- **实时数据推送**：每5秒推送一次最新市场价格数据（OHLCV）
- **动态K线构建**：策略内部维护5分钟K线状态，通过接收实时5秒数据动态构建
- **持续状态管理**：策略持续跟踪持仓、订单、K线状态
- **实时价格调整**：根据最新价格动态调整止损止盈

### 数据流向

```
CSV历史数据 → RealTimeDataStream（每5秒推送） → Strategy（动态K线构建） → 实时交易决策
```

## 项目文件结构

```
src/
├── types/                  # 类型定义
│   ├── tick.ts            # 5秒级实时数据类型
│   ├── candle.ts          # K线数据类型
│   ├── trade.ts           # 交易记录类型
│   └── config.ts          # 配置类型
├── data/                   # 数据处理模块
│   ├── data-loader.ts     # CSV数据加载器
│   └── realtime-stream.ts # 实时数据流（RxJS模拟WebSocket）
├── strategy/               # 策略模块
│   ├── candle-builder.ts  # 动态K线构建器
│   ├── indicators.ts      # 指标计算（IBS等）
│   ├── signals.ts         # 信号生成
│   └── single-bar.ts      # 主策略（含状态管理）
├── trading/                # 交易模块
│   ├── position-manager.ts # 仓位管理
│   ├── order-manager.ts   # 订单管理（实时价格调整）
│   └── gap-handler.ts     # 跳空处理
├── backtest/               # 回测模块
│   ├── engine.ts          # 回测引擎
│   ├── portfolio.ts       # 投资组合管理
│   └── metrics.ts         # 绩效指标计算
├── output/                 # 输出模块
│   ├── report-generator.ts # 报告生成器
│   ├── csv-exporter.ts    # CSV导出
│   └── chart-display.ts   # 图表展示
└── index.ts               # 主入口
```

## 关键组件设计思路

### 1. RealTimeDataStream（实时数据流）

**职责**：模拟WebSocket实时数据推送

```typescript
class RealTimeDataStream {
  // 每5秒推送一次市场数据，完全模拟真实交易环境
  createStream(): Observable<MarketTick>
}
```

**特性**：

- 使用RxJS timer控制推送频率
- 支持快速回测和实时模拟模式
- 自动处理交易时间段过滤
- 检测和标记价格跳空

### 2. CandleBuilder（动态K线构建器）

**职责**：策略内部动态维护5分钟K线状态

```typescript
class CandleBuilder {
  // 接收5秒数据，动态构建当前5分钟K线
  updateWithTick(tick: MarketTick): {
    currentCandle: Candle5Min,
    isCompleted: boolean,
    isNewCandle: boolean
  }
}
```

**特性**：

- 根据时间窗口动态构建K线
- 判断K线完成状态
- 处理跨会话K线构建
- 维护历史K线记录

### 3. SingleBarStrategy（主策略引擎）

**职责**：实现Pine Script策略逻辑，管理策略状态

```typescript
class SingleBarStrategy {
  // 处理每5秒的实时数据
  onTick(tick: MarketTick): void
  
  // K线完成时执行策略逻辑
  executeStrategyLogic(candle: Candle5Min): void
  
  // 实时更新止损止盈
  updateStopLossOrders(tick: MarketTick): void
}
```

**特性**：

- 严格按照Pine Script逻辑实现
- 实时响应价格变化
- 动态调整订单价格
- 处理跳空情况的价格调整

## 真实环境模拟特性

1. **WebSocket模拟**：使用RxJS Observable完全模拟WebSocket数据推送机制
2. **动态K线管理**：策略内部实时维护K线状态，而非使用预聚合数据
3. **实时价格响应**：每个tick都可以触发止损止盈价格调整
4. **精确时机捕捉**：利用5秒精度数据提供更准确的入场离场时机
5. **状态持续性**：完整模拟策略在真实环境中的状态管理

## 数据流处理流程

```mermaid
graph TD
    A[CSV历史数据] --> B[DataLoader]
    B --> C[RealTimeDataStream]
    C --> D[每5秒推送MarketTick]
    D --> E[Strategy.onTick]
    E --> F[CandleBuilder动态构建K线]
    F --> G{K线是否完成?}
    G -->|否| H[更新当前K线状态]
    G -->|是| I[执行策略逻辑]
    H --> J[实时调整止损止盈]
    I --> K[生成交易信号]
    K --> L[订单执行]
    L --> J
    J --> M[等待下一个Tick]
    M --> D
```

## 实现顺序

1. **类型定义**：定义所有接口和类型
2. **实时数据流**：实现RxJS数据推送机制
3. **动态K线构建器**：实现K线状态管理
4. **策略引擎**：实现Pine Script逻辑
5. **交易系统**：实现订单和仓位管理
6. **回测引擎**：协调各模块运行
7. **输出模块**：生成报告和图表

## 核心优势

- **真实性**：完全模拟真实交易环境的数据接收和处理方式
- **精确性**：利用5秒级数据提供更精确的交易时机
- **响应性**：实时响应价格变化，动态调整交易策略
- **完整性**：包含跳空处理、时间过滤等真实交易场景
