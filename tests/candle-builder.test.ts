import { test, expect, describe, beforeEach } from "bun:test";
import { CandleBuilder } from "../src/strategy/candle-builder.ts";
import { MarketTick } from "../src/types/tick.ts";

describe("CandleBuilder", () => {
  let candleBuilder: CandleBuilder;

  beforeEach(() => {
    candleBuilder = new CandleBuilder();
  });

  test("should create new candle from first tick", () => {
    const tick: MarketTick = {
      datetime: "2024-03-15 09:30:00",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    const result = candleBuilder.updateWithTick(tick);

    expect(result.isNewCandle).toBe(true);
    expect(result.isCompleted).toBe(false);
    expect(result.currentCandle.datetime).toBe("2024-03-15 09:30:00");
    expect(result.currentCandle.open).toBe(5000);
    expect(result.currentCandle.high).toBe(5010);
    expect(result.currentCandle.low).toBe(4995);
    expect(result.currentCandle.close).toBe(5005);
    expect(result.currentCandle.volume).toBe(100);
    expect(result.currentCandle.tickCount).toBe(1);
  });

  test("should update existing candle with new tick", () => {
    // 第一个tick - 创建新K线
    const tick1: MarketTick = {
      datetime: "2024-03-15 09:30:00",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick1);

    // 第二个tick - 更新同一K线
    const tick2: MarketTick = {
      datetime: "2024-03-15 09:30:05", // 同一5分钟窗口
      open: 5005,
      high: 5015, // 更高的高点
      low: 4990,  // 更低的低点
      close: 5012,
      volume: 120,
    };

    const result = candleBuilder.updateWithTick(tick2);

    expect(result.isNewCandle).toBe(false);
    expect(result.isCompleted).toBe(false);
    expect(result.currentCandle.open).toBe(5000); // 开盘价不变
    expect(result.currentCandle.high).toBe(5015); // 更新为更高值
    expect(result.currentCandle.low).toBe(4990);  // 更新为更低值
    expect(result.currentCandle.close).toBe(5012); // 更新收盘价
    expect(result.currentCandle.volume).toBe(220); // 累加成交量
    expect(result.currentCandle.tickCount).toBe(2);
  });

  test("should complete candle and start new one when time window changes", () => {
    // 第一个tick - 09:30:00窗口
    const tick1: MarketTick = {
      datetime: "2024-03-15 09:34:55", // 09:30-09:35窗口的最后几秒
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick1);

    // 第二个tick - 09:35:00窗口（新的5分钟窗口）
    const tick2: MarketTick = {
      datetime: "2024-03-15 09:35:00",
      open: 5005,
      high: 5015,
      low: 5000,
      close: 5012,
      volume: 120,
    };

    const result = candleBuilder.updateWithTick(tick2);

    expect(result.isCompleted).toBe(true); // 前一根K线完成
    expect(result.isNewCandle).toBe(true); // 开始新K线
    expect(result.currentCandle.datetime).toBe("2024-03-15 09:35:00");
    
    // 检查已完成的K线
    const completedCandles = candleBuilder.getCompletedCandles();
    expect(completedCandles.length).toBe(1);
    expect(completedCandles[0]!.isCompleted).toBe(true);
    expect(completedCandles[0]!.datetime).toBe("2024-03-15 09:30:00");
  });

  test("should handle session end marking", () => {
    const tick: MarketTick = {
      datetime: "2024-03-15 15:59:55",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
      isSessionEnd: true,
    };

    const result = candleBuilder.updateWithTick(tick);

    expect(result.currentCandle.isSessionEnd).toBe(true);
  });

  test("should detect gap when session starts", () => {
    // 前一个tick
    const tick1: MarketTick = {
      datetime: "2024-03-15 15:59:55",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
      isSessionEnd: true,
    };

    candleBuilder.updateWithTick(tick1);

    // 跳空开盘的tick
    const tick2: MarketTick = {
      datetime: "2024-03-15 17:00:00",
      open: 5020, // 跳空15点
      high: 5025,
      low: 5018,
      close: 5022,
      volume: 120,
      isSessionStart: true,
    };

    const result = candleBuilder.updateWithTick(tick2);

    expect(result.hasGap).toBe(true);
    expect(result.gapInfo).toBeDefined();
    expect(result.gapInfo!.hasGap).toBe(true);
    expect(result.gapInfo!.gapDirection).toBe("up");
  });

  test("should get current candle correctly", () => {
    const tick: MarketTick = {
      datetime: "2024-03-15 09:30:00",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick);

    const currentCandle = candleBuilder.getCurrentCandle();
    expect(currentCandle).toBeDefined();
    expect(currentCandle!.open).toBe(5000);
    expect(currentCandle!.isCompleted).toBe(false);
  });

  test("should get last completed candle", () => {
    // 创建并完成一根K线
    const tick1: MarketTick = {
      datetime: "2024-03-15 09:34:55",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick1);

    // 触发K线完成
    const tick2: MarketTick = {
      datetime: "2024-03-15 09:35:00",
      open: 5005,
      high: 5015,
      low: 5000,
      close: 5012,
      volume: 120,
    };

    candleBuilder.updateWithTick(tick2);

    const lastCandle = candleBuilder.getLastCompletedCandle();
    expect(lastCandle).toBeDefined();
    expect(lastCandle!.isCompleted).toBe(true);
    expect(lastCandle!.datetime).toBe("2024-03-15 09:30:00");
  });

  test("should force complete current candle", () => {
    const tick: MarketTick = {
      datetime: "2024-03-15 09:30:00",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick);

    const completedCandle = candleBuilder.forceCompleteCurrentCandle();
    
    expect(completedCandle).toBeDefined();
    expect(completedCandle!.isCompleted).toBe(true);
    expect(candleBuilder.getCurrentCandle()).toBe(null);
    expect(candleBuilder.getCompletedCandles().length).toBe(1);
  });

  test("should get recent candles correctly", () => {
    // 创建多根K线
    const ticks: MarketTick[] = [
      {
        datetime: "2024-03-15 09:29:55",
        open: 5000, high: 5010, low: 4995, close: 5005, volume: 100,
      },
      {
        datetime: "2024-03-15 09:34:55",
        open: 5005, high: 5015, low: 5000, close: 5010, volume: 120,
      },
      {
        datetime: "2024-03-15 09:39:55",
        open: 5010, high: 5020, low: 5005, close: 5015, volume: 110,
      },
    ];

    // 逐个处理tick，每个都会创建新的K线
    ticks.forEach((tick, index) => {
      candleBuilder.updateWithTick(tick);
      if (index < ticks.length - 1) {
        // 强制完成当前K线（除了最后一个）
        candleBuilder.forceCompleteCurrentCandle();
      }
    });

    const recentCandles = candleBuilder.getRecentCandles(2);
    expect(recentCandles.length).toBe(2);
    expect(recentCandles[0]!.open).toBe(5000);
    expect(recentCandles[1]!.open).toBe(5005);
  });

  test("should reset correctly", () => {
    const tick: MarketTick = {
      datetime: "2024-03-15 09:30:00",
      open: 5000,
      high: 5010,
      low: 4995,
      close: 5005,
      volume: 100,
    };

    candleBuilder.updateWithTick(tick);
    candleBuilder.forceCompleteCurrentCandle();

    expect(candleBuilder.getCompletedCandles().length).toBe(1);

    candleBuilder.reset();

    expect(candleBuilder.getCurrentCandle()).toBe(null);
    expect(candleBuilder.getCompletedCandles().length).toBe(0);
  });
});
