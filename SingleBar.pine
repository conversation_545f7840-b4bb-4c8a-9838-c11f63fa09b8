//@version=6
strategy("SingleBarFastMaget", overlay=true)

strategyDirection = input.session("都做","策略方向",["都做",'只做空','只做多'], group = "测试配置")

contractMultipler = input.int(5, "合约倍数", step = 1, group = "仓位管理")
maxLossValue = input.int(500, "最大单仓亏损", step = 1, group = "仓位管理")
longMinProfitPoint = input.float(1.25, "多头最小利润点数", step = 0.25, group = "仓位管理")
shortMinProfitPoint = input.float(1.25, "空头最小利润点数", step = 0.25, group = "仓位管理")

bullIBSTarget = input.int(70, "多头信号最低IBS值", step = 1, group = "入场配置")
bearIBSTarget = input.int(48, "空头信号最大IBS值", step = 1, group = "入场配置")
trendBarJudge = input.bool(false, '是否开启趋势K判断', group = "入场配置")
longMinEntityForTrendBar = input.float(0.5, "多头趋势K实体最小比例", step = 0.05, group = "入场配置")
shortMinEntityForTrendBar = input.float(0.5, "空头趋势K实体最小比例", step = 0.05, group = "入场配置")

isBullBar = close > open
isBearBar = close < open

ibs = math.round((close - low) / (high - low) * 100)
isTrendBar = trendBarJudge ?  math.abs(close - open) / math.max(math.abs(high - low), 0.0001) > (isBullBar ? longMinEntityForTrendBar : isBearBar ? shortMinEntityForTrendBar : 0.5) : true

longCondition = isBullBar and ibs > bullIBSTarget  and isTrendBar and high - close >= longMinProfitPoint and (strategyDirection == '都做' or strategyDirection == '只做多')
shortCondition = isBearBar and ibs < bearIBSTarget and isTrendBar and close - low >= shortMinProfitPoint and (strategyDirection == '都做' or strategyDirection == '只做空')


if strategy.position_size == 0
    strategy.cancel_all()
    if longCondition
        limitPrice = high
        stopPrice = low
        qty = math.floor(maxLossValue / (close - low) / contractMultipler)
        if qty > 1
            strategy.entry("Long",strategy.long, qty = qty)
            strategy.exit("Exit Long", 'Long', limit = limitPrice, stop = stopPrice - syminfo.mintick)
    if shortCondition
        limitPrice = low
        stopPrice = high
        qty = math.floor(maxLossValue / (high - close) / contractMultipler)
        if qty > 1
            strategy.entry("Short", strategy.short, qty = qty)
            strategy.exit("Exit Short", "Short", limit = limitPrice, stop = stopPrice + syminfo.mintick)
if session.islastbar
    // 收盘前强制平仓，避免隔夜跳空
    strategy.cancel_all()
    strategy.close_all(immediately = true)